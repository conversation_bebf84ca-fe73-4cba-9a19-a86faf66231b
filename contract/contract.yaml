openapi: 3.0.0
info:
  version: 3.0-SNAPSHOT
  title: identity-verification-service
paths:
  '/hello/{who}':
    get:
      tags:
        - example
      summary: Hello World
      operationId: hello
      description: takes a name as a parameter
      parameters:
        - name: who
          in: path
          description: name of person
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            'application/json':
              schema:
                $ref: '#/components/schemas/SampleResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/vehicle':
    get:
      tags:
        - vehicle
      summary: Vehicle retrievel service
      operationId: getVehicle
      description: takes vrm as query parameter
      parameters:
        - name: vrm
          in: query
          description: vrm id of vehicle
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            'application/json':
              schema:
                $ref: '#/components/schemas/Vehicle'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
    post:
      tags:
        - vehicle
      summary: service to add vehicle
      operationId: addVehicle
      description: send Vehicle object in request
      requestBody:
        description: Vehicle Request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Vehicle'
      responses:
        '201':
          description: Created
        default:
          $ref: '#/components/responses/ApiErrorResponse'
components:
  responses:
    ApiErrorResponse:
      description: Generic API error response
      content:
        application/problem+json:
          schema:
            $ref: "com.gumtree.shared-common.shared-api-base-contract.base-contract.yaml#/components/schemas/ApiError"
  schemas:
    SampleResponse:
      type: object
      description: API response
      properties:
        message:
          type: string
          description: some message
          example: Some example
    Vehicle:
      type: object
      description: Vehicle details
      properties:
        vrm:
          type: string
          description: unique vrm id of vehicle
          example: abc123
        make:
          type: string
          description: Manufacturer of vehicle
          example: Ford
        model:
          type: string
          description: model of vehicle
          example: Mustang
        fuelType:
          type: string
          description: fuel type of vehicle
          example: Petrol
        noOfDoors:
          type: integer
          description: door of vehicle
          example: 2
