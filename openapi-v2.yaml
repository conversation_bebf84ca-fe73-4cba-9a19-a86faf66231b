openapi: 3.0.0
info:
  title: 'Detected API'
  description: "[Download OAS (Open API Specification)](#)<br>\n\n**Last updated:** 10th January 2025, 13:00\n\n---\n\n## Introduction\n\nWelcome to the Detected API documentation. Our API follows RESTful standards to ensure consistent and predictable behavior.\n\n---\n\n## Environments\n\nThe API is accessible in the following environments:\n\n- **Production**\n  [https://profile-backend.detected.app/api/v2/public](https://profile-backend.detected.app/api/v2/public)\n\n---\n\n## Authentication\n\nOur API uses a simple API token for authentication. This token must be included in the `Authorization` header of every request using the Bearer authentication scheme.\n\n```http\nAuthorization: Bearer token_here\n```\n\n#### Obtaining the Token\nYou can retrieve your API token from the dashboard:\n\n1. Log in to your account.\n2. Navigate to the Account Setup section.\n3. Locate the API Token in the Integration Information section.\n\nEnsure that you keep your API token secure, as it provides access to your account API functionality.\n\n---\n\n## Webhooks\n\nOur API enables you to subscribe to profile-related events through webhooks.\n\n### Supported Webhooks\n\nIn version 2 of our API, the following webhook events are available:\n\n| **Event**          | **Description**                                         |\n|---------------------|---------------------------------------------------------|\n| `profile.created`   | Triggered when a new profile is created.                |\n| `profile.updated`   | Triggered when any updates are made to a profile.       |\n\n\n### Sample of Webhook\n```json\n{\n  \"event\": \"profile.created\",\n  \"timestamp\": \"2024-12-15T18:44:40.313335Z\",\n  \"data\": {\n    \"id\": \"09848a71-4219-4b68-85e1-c59c2eacf762\",\n    \"customer_reference\": \"andre22\",\n    \"company_name\": \"Detected Ltd\",\n    \"risk\": {\n      \"id\": \"********-6843-401d-9283-532fb4350b4f\",\n      \"label\": \"P2 review required\",\n      \"category\": \"Medium\"\n    },\n    \"team\": {\n      \"id\": 55,\n      \"label\": \"The A Team - edited (Compliance)\"\n    },\n    \"configuration\": null,\n    \"review_status\": \"Processed\",\n    \"assignee\": {\n      \"id\": \"auth0|60dc8a229d13ba006c5d51ab\",\n      \"name\": \"Lauren Sipple\"\n    },\n    \"review_at\": null,\n    \"updated_at\": \"2024-12-15 18:20:10\",\n    \"created_at\": \"2023-05-05 14:47:20\"\n  }\n}\n```\n\n### Configuration\n\nTo enable webhooks, follow these steps:\n\n1. Log in to your dashboard and navigate to the **Account Setup** section.\n2. Click on **Integration Information** or access it directly [here](https://monitor.staging-detected.app/integration-docs).\n3. Provide the following information:\n   - **URL**: The endpoint where webhook events should be posted.\n   - **Secret**: A string used to sign webhook requests for verification purposes.\n\n### Webhook Verification\n\nWebhook requests are signed to ensure authenticity. Each request includes a `Signature` header.\n\nTo verify the webhook:\n\n1. Generate a signature on your side using the following steps:\n   - Use the `sha256` algorithm.\n   - Combine the received JSON payload with the secret value provided in the dashboard.\n\n2. Compare your generated signature with the `Signature` header from the webhook request.\n\nIf the signatures match, the webhook is verified successfully.\n\n---\n\n## Versioning\n\nOur API follows **semantic versioning** principles:\n\n- **Major versions** are reflected in the endpoint (e.g., `/v2/`).\n- **Minor updates** and **patch fixes** are implemented without breaking existing integrations.\n\nIn the event of a breaking change, a new major version will be introduced to ensure compatibility.\n\n---\n\n## Rate Limiting\n\nOur API enforces rate limiting to ensure fair usage and optimal performance. The current rate limit is **50 requests per minute** per API token.\n\nIf you exceed this limit, you will receive a `429 Too Many Requests` response. To avoid being rate-limited:\n\n- Implement request queuing or throttling in your application.\n- Monitor your API usage to ensure it stays within the limit.\n\n### Rate-Limiting Response Headers\n\nEach API response includes the following headers to help you monitor your usage:\n\n- **`x-ratelimit-limit`**: The maximum number of requests allowed within the time window (e.g., `50`).\n- **`x-ratelimit-remaining`**: The number of requests remaining in the current time window.\n\n### Example Response Headers\n\n```http\nx-ratelimit-limit: 50\nx-ratelimit-remaining: 10\n\n"
  version: v2
servers:
  -
    url: 'https://profile-backend.detected.app/api/v2/public'
    description: Production
paths:
  '/profiles/{uuid}/checklists':
    get:
      tags:
        - Checklists
      summary: 'Retrieve profile checklists'
      description: "### Introduction\nThis endpoint returns the list of checklists associated with profile<br>\n\n### Pagination\n\nThis endpoint is paginated.\n\n"
      operationId: 7d5bc7f98df5b25c4202c6a29a0d61c7
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `created_at`\n2. `updated_at`\n3. `id`\n4. `name`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: string
        -
          name: name
          in: query
          description: 'The `name` parameter allows to filter checklists by their name'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of checklists', type: array, items: { $ref: '#/components/schemas/ChecklistResource' } }
                  links: { $ref: '#/components/schemas/LinksResource' }
                  meta: { $ref: '#/components/schemas/MetaResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}/checklists/{id}':
    get:
      tags:
        - Checklists
      summary: 'Retrieve a profile checklist by id'
      description: 'Retrieve profile checklist by id'
      operationId: c9e705c4d3bae53ce6205c58882f854b
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
        -
          $ref: '#/components/parameters/id'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/ChecklistResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}/analysis':
    get:
      tags:
        - Analysis
      summary: 'Retrieve list of analysis for a profile'
      description: "### Introduction\nThis endpoint returns the results of copilot analysis of a profile<br>\n"
      operationId: a0a2bc4774b6854208be3b7037314170
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of checklists', type: array, items: { $ref: '#/components/schemas/AnalysisResource' } }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /documents:
    get:
      tags:
        - Documents
      summary: 'Retrieve documents'
      description: "### Introduction\nThis endpoint returns a list of documents<br>"
      operationId: f90fe4444f34d192763febd41979d140
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: number
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `created_at`\n2. `updated_at`\n3. `id`\n4. `profile_id`\n5. `document_id`\n6. `category`\n7. `name`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
        -
          name: profile_id
          in: query
          description: 'The `profile_id` parameter allows to filter documents by their profile'
          schema:
            type: string
        -
          name: document_id
          in: query
          description: 'The `document_id` parameter allows to filter documents by their document_id'
          schema:
            type: string
        -
          name: category
          in: query
          description: 'The `category` parameter allows to filter documents by their category'
          schema:
            type: string
        -
          name: name
          in: query
          description: 'The `name` parameter allows to filter documents by their name'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of documents', type: array, items: { $ref: '#/components/schemas/DocumentListResponse' } }
                  links: { $ref: '#/components/schemas/LinksResource' }
                  meta: { $ref: '#/components/schemas/MetaResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  '/documents/{id}':
    get:
      tags:
        - Documents
      summary: 'Retrieve a document by id'
      description: "### Introduction\nThis endpoint returns a document details<br>"
      operationId: e58e42696daf0c0d06c54728f3da6615
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/id'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/DocumentViewResponse' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}/forms':
    get:
      tags:
        - Forms
      summary: 'Retrieve profile forms'
      description: "### Introduction\nThis endpoint returns the list of forms associate with a profile<br>"
      operationId: 927960b2232c3fba54af964e326af75d
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: string
        -
          name: label
          in: query
          description: 'The `label` parameter allows to filter forms by their label'
          schema:
            type: string
        -
          name: reference
          in: query
          description: 'The `reference` parameter allows to filter forms by their reference'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of forms', type: array, items: { $ref: '#/components/schemas/FormResource' } }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}/forms/{id}':
    get:
      tags:
        - Forms
      summary: 'Retrieve a profile form by id'
      description: "### Introduction\nThis endpoint returns a form associate with a profile<br>\n\n"
      operationId: fb948d6febce0fb9e9f5c273b64ef890
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
        -
          $ref: '#/components/parameters/id'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/FormResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
  '/representatives/{uuid}/verifications':
    get:
      tags:
        - Representatives
      summary: 'Retrieves a list of verifications for a representative'
      description: "## Introduction\nThe verifications endpoint retrieves a list of verification types available for a given representative.<br>\n\nCurrently, the supported verification types are:\n\n1. `idv`\n2. `id3`\n3. `digital_footprint`\n\nEach type of verification can use different providers, the data returned under the `info` key is specific to each provider. While we provide examples of how each response might look, please note that the structure and content of this data are determined by the external providers and may change without notice.\n"
      operationId: d49cb5421a3d7f6d3dcd55f826aab59d
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
        -
          name: type
          in: query
          description: 'The `type` parameter allows to filter verifications by their type. Multiple types can be provided by separating them with a comma.'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/PersonVerificationsResourceResponse' }
                type: object
              examples:
                'When type is `idv` and provider `id_scan`':
                  summary: 'When type is `idv` and provider `id_scan`'
                  value: { type: idv, provider: id_scan, info: { sessionId: 0d679c0a-d0ef-4fb1-88a9-680baf5b982f, sessionToken: upload, state: closed, result: { name: 'Jon Doe', state: COMPLETE, identity_status: APPROVED, identity_fail_reason: '', liveness_status: NOT-PERFORMED-UPLOAD, face_match_status: NOT-PERFORMED-UPLOAD, face_match_fail_reason: '', id_requested_at: null, dob_on_document: 27/10/1997, expiration_date: null, expiration_status: '', journey_id: 0d679c0a-d0ef-4fb1-88a9-680baf5b982f, provider: id_scan, can_download_report: true, can_download_until: 27/06/2025 }, createdAt: '2025-05-27 09:19:09' } }
                'When type is `idv` and provider `yoti`':
                  summary: 'When type is `idv` and provider `yoti`'
                  value: { type: idv, provider: yoti, info: { provider: yoti, sessionId: 35b-8dbe7fdce85f, sessionToken: 97b83-45fd-bd6d-cb76d96ac778, iframeUrl: 'https://api.yoti.com/sandbox/idverify/v1/web/index.html?sessionToken=97bad-cb76d96ac778&sessionID=35ba23321', QRCode: null, state: closed, result: { name: 'John Doe', state: COMPLETE, identity_status: APPROVED, identity_fail_reason: '', liveness_status: APPROVED, face_match_status: APPROVED, face_match_fail_reason: '', id_requested_at: null, dob_on_document: 01/06/1986, expiration_date: null, expiration_status: '', journey_id: null, provider: null, can_download_report: false, can_download_until: null }, createdAt: '2025-02-27 10:27:46' } }
                'When type is `digital_footprint` and provider `risk_seal`':
                  summary: 'When type is `digital_footprint` and provider `risk_seal`'
                  value: { type: digital_footprint, provider: id_scan, info: { name: 'Jon doe', phone_number: '+0192993103901', phone_statuses: { skype: 'not registered', amazon: registered, google: registered, foundit: registered, twitter: 'no result', facebook: registered, whatsapp: registered, instagram: registered, microsoft: registered }, email_address: <EMAIL>, email_statuses: { vk: 'not registered', zoho: 'not registered', adobe: registered, anydo: 'not registered', apple: registered, asana: 'not registered', kommo: 'not registered', px500: 'not registered', quora: registered, rappi: 'not registered', skype: registered, smule: 'not registered', vimeo: 'not registered', yahoo: 'not registered', amazon: registered, change: registered, deezer: 'not registered', disney: 'not registered', envato: 'not registered', flickr: 'not registered', github: 'not registered', google: registered, jooble: 'not registered', komoot: 'not registered', replit: 'not registered', strava: 'not registered', talent: 'not registered', trello: 'not registered', tumblr: 'not registered', vivino: 'not registered', zillow: 'not registered', bitmoji: 'not registered', dropbox: 'not registered', firefox: 'not registered', foundit: registered, hubspot: 'not registered', myspace: 'not registered', netflix: 'not registered', patreon: 'not registered', spotify: registered, twitter: registered, autodesk: 'not registered', basecamp: 'not registered', duolingo: 'not registered', europcar: 'not registered', facebook: registered, gravatar: 'not registered', infojobs: 'not registered', lastpass: 'not registered', linkedin: 'not registered', snapchat: 'not registered', atlassian: registered, deliveroo: 'not registered', grammarly: 'not registered', instacart: 'not registered', instagram: registered, microsoft: registered, pinterest: registered, treehouse: 'not registered', wordpress: 'not registered', codecademy: 'not registered', eventbrite: 'not registered', freelancer: 'not registered', bodybuilding: 'not registered', myfitnesspal: 'not registered' }, photos: ['https://vault.riskseal.io/randompicture-id.jpg'], email_age: '100', country: US, gender: null, trust_score: 90%, suspicious: false } }
                'When type is `id3` and provider `id3` by `gbg`':
                  summary: 'When type is `id3` and provider `id3` by `gbg`'
                  value: { type: id3, provider: id3, info: { id: 85, config: { id: 1, name: 'Detected Light UK IDV', description: 'Light check with no credit checking', id3_profile_id: cbe825a4679d, created_at: '2024-12-23T11:59:12.000000Z', updated_at: '2024-12-23T11:59:12.000000Z' }, person_uuid: 4ea423bd-b1e0-4966-9acb-a5a6029741e2, request: { subject: { person: { firstName: 'Jon doe', lastNames: [Meacci], dateOfBirth: '1973-08-01' } }, profiles: [{ id: cbe804f5a4679d, version: 0 }] }, response: [{ result: { score: 0, decision: REFER, itemCheckResults: [{ id: 155, name: 'UK Credit Header (AML)', flags: { pass: Nomatch, alert: Nomatch, person: { address: Nomatch, firstName: Nomatch, lastNames: Nomatch, dateOfBirth: Nomatch } }, description: 'UK Credit Header Database.  Provides authentication of name, address and date of birth against Credit Header information for an Anti Money Laundering Check.', resultCodes: [{ code: 261, type: Comment, description: 'Title not supplied by user' }, { code: 101, type: Comment, description: 'No middle initial specified by user.' }, { code: 151, type: Comment, description: 'First year of residence for address #1 not supplied by user.' }, { code: 161, type: Comment, description: 'Last year of residence for address #1 not supplied by user. Applying default.' }, { code: 910, type: Comment, description: 'No/Insufficient address supplied.  This is a mandatory field and so no authentication was carried out for this item check.' }, { code: 500, type: Comment, description: 'Check against the Credit Database not carried out due to insufficient valid information supplied by the user.' }] }] }, country: UK, profile: { id: cbe804f1825a4679d, name: IDV, state: Effective, version: 1, revision: 5 }, timestamp: '2025-03-03T10:05:55.0905295+00:00', authenticationID: 5a17-bce6-486852308cf3 }], status: complete, created_at: '2025-03-03T10:05:53.000000Z', updated_at: '2025-03-03T10:05:56.000000Z' } }
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
  /representatives:
    get:
      tags:
        - Representatives
      summary: 'Retrieve associated representatives to the current account'
      description: "### Introduction\nThe representatives endpoint allows you to search for individuals, who may or may not be associated with existing profiles. The results include both natural persons and legal entities, which may, in some cases, represent a company rather than an individual.<br>\n\nThis includes individuals or legal entities added via the portal, case management, or converted from external data sources.\n\nCurrently, the following types of individuals are supported:\n\n1. `director`\n2. `identity`\n3. `ownership`\n4. `declaratory`\n5. `principal`\n6. `director_company`\n\n### Pagination\n\nThis endpoint is paginated.\n\n"
      operationId: 4bc7424550b7b4dc4beb0dc398fad418
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `created_at`\n2. `updated_at`\n3. `id`\n4. `email`\n5. `country`\n6. `types`, this can be a single value or a comma separated list e.g `principal,director`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: string
        -
          name: is_company
          in: query
          description: 'The `is_company` parameter allows you to filter for individuals who are legal entities rather than natural persons.'
          schema:
            type: string
        -
          name: customer_reference
          in: query
          description: 'Filter by `customer_reference`. If provided and valid, the response will return a single result.'
          schema:
            type: string
        -
          name: profile_id
          in: query
          description: 'The `profile_id` parameter allows to filter people by their assigned profile/case'
          schema:
            type: string
        -
          name: email
          in: query
          description: 'Filter by `email`'
          schema:
            type: string
        -
          name: types
          in: query
          description: 'The `type` parameter allows you to filter people based on their assigned types. Note that some individuals may have multiple types assigned to them.'
          schema:
            type: string
            enum:
              - director
              - identity
              - ownership
              - declaratory
              - principal
              - director_company
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of representatives', type: array, items: { $ref: '#/components/schemas/RepresentativesResource' } }
                  links: { $ref: '#/components/schemas/LinksResource' }
                  meta: { $ref: '#/components/schemas/MetaResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
    post:
      tags:
        - Representatives
      summary: 'Create a representative'
      description: "### Introduction\nThis endpoint allows you to create representatives associated with a specific profile. You must include the `profile_id` of the profile to which the representative will be linked. Both natural persons and legal entities are supported.<br>\n\nCurrently, the following types of representatives are supported:\n\n1. `director`\n2. `identity`\n3. `ownership`\n4. `declaratory`\n5. `principal`\n6. `director_company`\n"
      operationId: b541cddd0764f6924d11b4ff787ae9ec
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PersonCreateRequest'
      responses:
        '201':
          description: Created
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/RepresentativesResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  '/representatives/{uuid}':
    get:
      tags:
        - Representatives
      summary: 'Retrieves a person by uuid'
      description: 'Retrieves a representative'
      operationId: 5be1b0c7c945a48fe0566e9b6778c6d9
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/RepresentativesResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
  /profiles:
    get:
      tags:
        - Profiles
      summary: 'Retrieves list of profiles'
      description: "### Introduction <br>\n\nRetrieve profiles associated with the current account. You can optionally apply search filters to narrow down the results.\n\nThis endpoint is paginated.\n"
      operationId: 5b1ecce433c6ba5699ce22c2218b9dba
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `id`\n2. `configuration_id`\n3. `review_status`\n4. `created_at`\n5. `updated_at`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: string
        -
          name: review_status
          in: query
          description: 'The `review_status` parameter allows you to filter profiles based on their `review_status` types.'
          schema:
            type: string
            enum:
              - Processed
              - 'Needs Review'
              - 'Additional Info Required'
              - Approved
              - Declined
              - Archived
        -
          name: team_id
          in: query
          description: 'The `team_id` parameter filters profiles by their team'
          schema:
            type: string
        -
          name: assignee_id
          in: query
          description: 'The `assignee_id` parameter filters profiles by their assignee user'
          schema:
            type: string
        -
          name: risk_id
          in: query
          description: 'The `risk_id` parameter filters profiles by their risk'
          schema:
            type: string
        -
          name: customer_reference
          in: query
          description: 'The `customer_reference` parameter filters profiles by customer reference'
          schema:
            type: string
        -
          name: configuration_id
          in: query
          description: 'The `configuration_id` parameter filters profiles by their assigned configuration'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of profiles', type: array, items: { $ref: '#/components/schemas/ProfileListResource' } }
                  links: { $ref: '#/components/schemas/LinksResource' }
                  meta: { $ref: '#/components/schemas/MetaResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
    post:
      tags:
        - Profiles
      summary: 'Create a new profile'
      description: 'Creates a new profiles either from a search result or by manual details provided'
      operationId: create-profile-endpoint
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProfileRequest'
            examples:
              'Create a new profile from search results':
                summary: 'Create a new profile from search results'
                value:
                  lookup_id: 9120
                  response_id: 1
                  customer_reference: my-new-customer-reference
                  domain: detected.co
              'Create a new profile manually':
                summary: 'Create a new profile manually'
                value:
                  customer_reference: my-new-customer-reference
                  name: 'Microsoft Inc'
                  domain: microsoft.com
                  company_number: '*********'
                  address: { country_code: US, city: WA }
      responses:
        '200':
          description: OK
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/ProfileListResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}/domain':
    get:
      tags:
        - Profiles
      summary: 'View a profile domain'
      description: 'View detailed information about a profile domain, including whois, social, online presence and scam advisor analysis.'
      operationId: 68304a0a248b6141fe445ecacbf50b96
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/ProfileDomainResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
    post:
      tags:
        - Profiles
      summary: 'Create a new profile domain'
      description: 'Creates a new profile domain if does not exist.'
      operationId: create-profile-domain-endpoint
      parameters:
        -
          name: uuid
          in: path
          description: 'The UUID of the profile.'
          required: true
          schema:
            type: string
        -
          $ref: '#/components/parameters/Authorization'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProfileDomainRequest'
            examples:
              'Create a new profile domain':
                summary: 'Create a new profile domain'
                value:
                  domain: example.com
      responses:
        '202':
          description: 'Accepted for processing'
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}/portal-users':
    put:
      tags:
        - Profiles
      summary: 'Assign a portal user to a profile'
      description: "### Introduction\nThis endpoint allows you to assign portal users to a profile<br>\n\nYou can get the list of portal users by calling the [Get portal users](#operations-Users-get-portal-users-endpoint) endpoint, alternatively you can create a new portal user by calling the [Create a new portal user](#operations-Users-create-portal-user-endpoint) endpoint.\n\nA portal user can have one of the following roles:\n- `admin` - Grants access to the main onboarding portal\n\nBy default, if no role is sent, an admin is role is assigned.\n<br>"
      operationId: 14793a9807e457c91b346e65df09d9a0
      parameters:
        -
          name: uuid
          in: path
          description: 'The UUID of the profile.'
          required: true
          schema:
            type: string
        -
          $ref: '#/components/parameters/Authorization'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignProfileUserRequest'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/PortalUserViewResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  '/profiles/{uuid}':
    get:
      tags:
        - Profiles
      summary: 'View a profile'
      description: "View detailed information about a profile, including company details, screenings, and more. <br>\n\n### Data source providers\nCompany data is retrieved from various data providers, each delivering information in different formats. While some of this data is processed through a normalization interface to ensure consistency regardless of the provider, not all data can be normalized. As a result, the payload may include undocumented data. <br>\n\nAll data under the `data.company` key will either contain information, be `null`, or be an empty array `[]`. Note that we cannot guarantee the presence of data, as this depends on the availability and completeness of information from the data providers.\n"
      operationId: 8f62ce17008beef766982c01243b184f
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/ProfileResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
    patch:
      tags:
        - Profiles
      summary: 'Updates a profile'
      description: 'Can update customer_reference, risk, configuration, team, assignee'
      operationId: e9f9efbaf3c1186981085c70d0807e82
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/uuid'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProfileUpdateRequest'
            examples:
              'Assign an Assignee to a profile':
                summary: 'Assign an Assignee to a profile'
                value:
                  assignee_id: auth0|60dc8a229d13ba006c5d51ab
              'Unassign an Assignee from a profile':
                summary: 'Unassign an Assignee from a profile'
                value:
                  assignee_id: null
              'Assign a new review status':
                summary: 'Assign a new review status'
                value:
                  review_status: Processed
              'Unassign an risk from a profile':
                summary: 'Unassign an risk from a profile'
                value:
                  risk_id: null
              'Assign an risk to a profile':
                summary: 'Assign an risk to a profile'
                value:
                  risk_id: ********-6843-401d-9283-532fb4350b4f
              'Assign an team to a profile':
                summary: 'Assign an team to a profile'
                value:
                  team_id: 55
              'Unassign an team from a profile':
                summary: 'Unassign an team from a profile'
                value:
                  team_id: null
              'Assign an configuration to a profile':
                summary: 'Assign an configuration to a profile'
                value:
                  configuration_id: 55
              'Unassign an configuration from a profile':
                summary: 'Unassign an configuration from a profile'
                value:
                  configuration_id: null
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/ProfileResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  /search:
    post:
      tags:
        - Search
      summary: 'Search for a entity.'
      description: "### Introduction <br>\nOur search functionality enables you to explore company data using various criteria. The software queries multiple data providers to deliver the most relevant results based on your search. Behind the scenes, we perform fuzzy matching and score the results against the search criteria. Note that while some data providers also perform fuzzy matching, others do not. <br>\n\nThe returned results will include details about which providers supplied the data.<br>\n\n### Importing search result\n\nWe offer the ability to automatically create a profile from search results. For more information and examples, please refer to our `POST v2/profiles` endpoint documentation.\n"
      operationId: search
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchRequest'
            examples:
              'Search for a company in United Kingdom (GB).':
                summary: 'Search for a company in United Kingdom (GB).'
                value:
                  type: company
                  name: 'detected ltd'
                  address: { country_code: GB, city: London }
              'Search for a company in United States of America (US).':
                summary: 'Search for a company in United States of America (US).'
                value:
                  type: company
                  name: 'Microsoft Inc'
                  address: { country_code: US, city: 'Washington D.C', state: WA }
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  /portal-users/activity:
    get:
      tags:
        - Users
      summary: 'Summary of portal users activity for a given profile'
      description: "### Introduction\nThis endpoint allows you to retrieve activity performed by portal users associated with a specific profile or profiles.<br>\n\nBy default, it returns all activity types for all portal users associated with the currently authenticated account. You can narrow the results using filters such as `profile_id`.\n\nCurrently, the following `types` values are supported:\n\n- `access_log`: Information about user login events, including IP address, location, browser, and session information.\n\nAdditional activity types may be introduced in the future to track actions such as form submissions, updates, or permission changes.\n\nThis endpoint is paginated.\n<br>"
      operationId: ecca5ca24afe66b27788e5d6cdbac052
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          name: id
          in: query
          description: 'Id of the portal activity.'
          required: false
          schema:
            type: string
            nullable: false
        -
          name: user_id
          in: query
          description: 'User id'
          required: false
          schema:
            type: string
            nullable: false
        -
          name: profile_id
          in: query
          description: 'Filter results by profile id'
          required: false
          schema:
            type: string
            nullable: false
        -
          name: email
          in: query
          description: 'Filter by a user email'
          required: false
          schema:
            type: string
            nullable: false
        -
          name: ip
          in: query
          description: 'Filter by a specific IP address'
          required: false
          schema:
            type: string
            nullable: false
        -
          name: types
          in: query
          description: 'Types of activities to filter by with'
          required: false
          schema:
            type: string
            nullable: false
        -
          name: name
          in: query
          description: 'Name of portal user'
          required: false
          schema:
            type: string
            nullable: false
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `id`\n2. `created_at`\n3. `updated_at`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/ProfilePortalUserActivityResponse' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  /portal-users:
    get:
      tags:
        - Users
      summary: 'Retrieve associated portal users to the current account'
      description: "### Introduction\nThis endpoint returns a paginated list of portal users associated with the account.<br>\n\nThis endpoint is paginated.\n"
      operationId: 311f444bb9a31777ae784b6b72746514
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `email`\n2. `name`\n3. `id`\n3. `profile_id`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: string
        -
          name: name
          in: query
          description: 'The `name` parameter filters user by their name'
          schema:
            type: string
        -
          name: email
          in: query
          description: 'The `email` parameter filters user by their email'
          schema:
            type: string
        -
          name: profile_id
          in: query
          description: 'The `profile_id` parameter filters user assigned to given profile'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of users', type: array, items: { $ref: '#/components/schemas/PortalUserViewResource' } }
                  links: { $ref: '#/components/schemas/LinksResource' }
                  meta: { $ref: '#/components/schemas/MetaResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
    post:
      tags:
        - Users
      summary: 'Create a new portal user'
      description: 'Creates a new portal user'
      operationId: create-portal-user-endpoint
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PortalUserCreateRequest'
            examples:
              'Create a new portal user':
                summary: 'Create a new portal user'
                value:
                  email: <EMAIL>
      responses:
        '201':
          description: Created
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/PortalUserViewResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/422'
        '429':
          $ref: '#/components/responses/429'
  '/portal-users/{userId}':
    get:
      tags:
        - Users
      summary: 'Retrieve associated portal users to the current account'
      description: 'Retrieve a portal user by id'
      operationId: get-portal-users-endpoint
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/userId'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/PortalUserViewResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /users:
    get:
      tags:
        - Users
      summary: 'Retrieve associated case management users to the current account'
      description: "### Introduction\nReturn the list of case management users associate with the account<br>\n\nThis endpoint is paginated.\n"
      operationId: 1c7e285d5652d133de701c65e6ae4e62
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/order_by'
        -
          name: sort_by
          in: query
          description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n\nCurrently, we support these fields:\n1. `email`\n2. `name`\n3. `id`\n4. `blocked`\n"
          schema:
            type: string
        -
          $ref: '#/components/parameters/page'
        -
          name: id
          in: query
          description: 'The `id` parameter filters by an internal `id`. If a match is found, the response will return a single result.'
          schema:
            type: string
        -
          name: blocked
          in: query
          description: 'The `blocked` parameter filters users by their blocked state'
          schema:
            type: string
            enum:
              - 'true'
              - 'false'
        -
          name: name
          in: query
          description: 'The `name` parameter filters user by their name'
          schema:
            type: string
        -
          name: email
          in: query
          description: 'The `email` parameter filters user by their email'
          schema:
            type: string
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { description: 'List of users', type: array, items: { $ref: '#/components/schemas/UserListResource' } }
                  links: { $ref: '#/components/schemas/LinksResource' }
                  meta: { $ref: '#/components/schemas/MetaResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  '/users/{userId}':
    get:
      tags:
        - Users
      summary: 'Retrieves a case management user by a given user id'
      description: 'Retrieve a case management user'
      operationId: 11c18447ef095af9b895ea63e0318d87
      parameters:
        -
          $ref: '#/components/parameters/Authorization'
        -
          $ref: '#/components/parameters/userId'
      responses:
        '200':
          description: Ok
          headers:
            X-RateLimit-Limit:
              $ref: '#/components/headers/X-RateLimit-Limit'
            X-RateLimit-Remaining:
              $ref: '#/components/headers/X-RateLimit-Remaining'
          content:
            application/json:
              schema:
                properties:
                  data: { $ref: '#/components/schemas/UserViewResource' }
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
components:
  schemas:
    LinksResource:
      title: 'Links Resource'
      properties:
        first:
          description: first
          type: string
          example: 'https://example.com/api/v2/public/resourceName?sort_by=post_code&page=1'
          nullable: false
        last:
          description: first
          type: string
          example: 'https://example.com/api/v2/public/resourceName?sort_by=post_code&page=10'
          nullable: false
        prev:
          description: prev
          type: string
          example: 'https://example.com/api/v2/public/resourceName?sort_by=post_code&page=1'
          nullable: false
        next:
          description: prev
          type: string
          example: 'https://example.com/api/v2/public/resourceName?sort_by=post_code&page=3'
          nullable: false
      type: object
    UnassignAssigneeFromProfile:
      title: 'Unassign Assignee From Profile'
      properties:
        assignee_id:
          description: 'Assignee id'
          type: string
          example: null
          nullable: true
      type: object
    AssigneeToProfile:
      title: 'Assignee to Profile'
      properties:
        assignee_id:
          description: 'Assignee id'
          type: string
          example: auth0|60dc8a229d13ba006c5d51ab
          nullable: true
      type: object
    MetaResource:
      title: 'Meta Resource'
      properties:
        current_page:
          description: current_page
          type: number
          example: 2
          nullable: false
        from:
          description: from
          type: number
          example: 31
          nullable: false
        last_page:
          description: last_page
          type: number
          example: 10
          nullable: false
        links:
          description: links
          type: array
          items:
            properties:
              url:
                description: url
                type: string
                example: 'https://example.comapi/v2/public/resourceName?sort_by=post_code&page=1'
                nullable: false
              label:
                description: label
                type: string
                example: '&laquo; Previous'
                nullable: false
              active:
                description: active
                type: boolean
                example: false
                nullable: false
            type: object
          nullable: false
        path:
          description: path
          type: string
          example: 'https://example.comapi/v2/public/resourceName'
          nullable: false
        per_page:
          description: per_page
          type: number
          example: 30
          nullable: false
        to:
          description: to
          type: number
          example: 60
          nullable: false
        total:
          description: total
          type: number
          example: 274
          nullable: false
      type: object
    ChecklistResource:
      title: 'Checklist Resource'
      required:
        - id
        - name
      properties:
        id:
          description: ID
          type: number
          example: 10
          nullable: false
        name:
          description: Name
          type: string
          example: 'My Checklist'
          nullable: false
        sections:
          description: Sections
          type: array
          items:
            properties:
              id:
                description: ID
                type: number
                example: 10
                nullable: false
              name:
                description: Name
                type: string
                example: 'Section Name 1'
                nullable: false
              tasks:
                description: Tasks
                type: array
                items:
                  properties: { id: { description: ID, type: number, example: 10, nullable: false }, name: { description: Name, type: string, example: 'Approve Profile', nullable: false }, team: { description: Team, properties: { id: { description: ID, type: number, example: 10, nullable: false }, name: { description: Name, type: string, example: 'Compliance Team', nullable: false }, members_count: { description: 'Members count', type: number, example: 2, nullable: false } }, type: object, nullable: true } }
                  type: object
              status:
                description: Status
                type: string
                enum:
                  - exempt
                  - failed
                  - passed
                  - needs_review
                example: exempt
                nullable: false
              total_comments:
                description: 'Total Comments'
                type: number
                example: 1
                nullable: false
              member_belongs_to_team:
                description: 'Member belong to team'
                type: boolean
                example: true
                nullable: false
            type: object
        uncompleted_tasks:
          description: 'Uncompleted tasks'
          type: number
          example: 10
          nullable: false
        completed_tasks:
          description: 'Completed tasks'
          type: number
          example: 10
          nullable: false
      type: object
    AnalysisResource:
      title: 'Analysis Resource'
      required:
        - id
        - score
        - name
        - rules
        - status
        - created_at
      properties:
        score:
          type: number
          example: 30
          nullable: false
        status:
          type: string
          enum:
            - failed
            - completed
            - pending
            - missing_data
          example: completed
          nullable: false
        risk:
          type: string
          example: Low
          nullable: false
        created_at:
          type: string
          example: '08 Apr 2024'
          nullable: false
        report:
          type: array
          items:
            properties:
              name:
                type: string
                example: 'Company AML'
                nullable: false
              result:
                type: array
                items:
                  required: [id, score, state, label, result]
                  properties: { id: { type: string, example: 9bc270b4-f9f8-4167-8764-06551f10276d, nullable: false }, score: { type: number, example: 10, nullable: false }, state: { type: string, enum: [skipped, analysed, failed], example: analysed, nullable: false }, label: { type: string, example: 'Company Sanctions', nullable: false }, description: { type: string, example: 'Lorem Ipsum comes from a latin text written in 45BC by Roman statesman, lawyer, scholar, and philosopher, Marcus Tullius Cicero.', nullable: true }, result: { type: string, example: '2 sanctions have been found.', nullable: false } }
                  type: object
                nullable: false
            type: object
          nullable: false
      type: object
    DocumentListResponse:
      title: 'Document List Response'
      required:
        - id
        - profile_id
        - library_id
        - category
        - name
        - created_at
        - updated_at
      properties:
        id:
          description: 'document id'
          type: number
          example: '9201'
          nullable: false
        profile_id:
          description: 'profile uuid'
          type: string
          example: e8f29c1d-125a-4e36-92f9-f5b43ccf1dc7
          nullable: false
        library_id:
          description: 'document library id'
          type: string
          example: a776b90b-d100-4f01-8be0-420b5ac8240a
          nullable: false
        category:
          description: category
          type: string
          example: 'ID Card'
          nullable: false
        name:
          description: name
          type: string
          example: 'ID Card'
          nullable: false
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    DocumentViewResponse:
      title: 'Document View Response'
      required:
        - id
        - profile_id
        - document_id
        - category
        - name
        - link
        - file_type
        - link_expires_at
        - created_at
        - updated_at
      properties:
        id:
          description: id
          type: number
          example: 1
          nullable: false
        profile_id:
          description: 'profile uuid'
          type: string
          example: e8f29c1d-125a-4e36-92f9-f5b43ccf1dc7
          nullable: false
        document_id:
          description: 'document id'
          type: string
          example: a776b90b-d100-4f01-8be0-420b5ac8240a
          nullable: false
        category:
          description: category
          type: string
          example: 'ID Card'
          nullable: false
        name:
          description: name
          type: string
          example: 'ID Card'
          nullable: false
        link:
          description: 'document link'
          type: string
          example: 'https://example.com/bucket/XPQmInrqYTIxTnRucMzzg2Ku7A6GWdngpyUB09Vu.png'
          nullable: false
        file_type:
          description: 'file type'
          type: string
          example: jpeg
          nullable: false
        link_expires_at:
          description: 'document link expiry time'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    FormResource:
      title: 'Form Resource'
      required:
        - id
        - label
        - answers
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: number
          example: 1
          nullable: false
        label:
          description: Label
          type: string
          example: 'Signup form'
          nullable: false
        reference:
          description: Reference
          type: string
          example: my-reference
          nullable: false
        answers:
          description: Answers
          type: array
          items:
            properties:
              label:
                description: Label
                type: string
                example: 'What is you phone number'
                nullable: false
              answer:
                description: Answer
                type: string
                example: '0000001111111'
                nullable: false
            type: object
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    PersonCreateRequest:
      title: 'Person Create Request'
      required:
        - types
        - email
        - name
        - profile_id
      properties:
        name:
          required:
            - first_name
            - last_name
          properties:
            first_name:
              description: Firstname
              type: string
              example: Jon
              nullable: false
            middle_name:
              description: 'Middle name'
              type: string
              example: F
              nullable: false
            last_name:
              description: Lastname
              type: string
              example: Doe
              nullable: false
          type: object
          nullable: false
        email:
          description: Email
          type: string
          example: <EMAIL>
          nullable: false
        profile_id:
          description: 'Profile ID'
          type: string
          example: 0192-0ol1p-1299al-122
          nullable: false
        types:
          description: 'Types of the representative'
          type: array
          items:
            type: string
            enum:
              - director
              - identity
              - ownership
              - declaratory
              - principal
              - director_company
          example:
            - director
            - identity
          nullable: false
        dob:
          description: 'Date of birth, the format is yyyy-mm-dd'
          type: string
          example: '2025-05-04'
          nullable: false
        professional_profile:
          description: 'Professional Profile'
          type: string
          example: 'https://professional-website.com'
          nullable: false
        job_title:
          description: 'Job title'
          type: string
          example: 'Software Engineer'
          nullable: false
        residence_country_code:
          description: 'Country of residence, ISO 3166 alpha-2 country code'
          type: string
          example: GB
          nullable: false
        birth_country_code:
          description: 'Country of residence, ISO 3166 alpha-2 country code'
          type: string
          example: GB
          nullable: false
        nationality:
          description: 'Nationality of individual'
          type: string
          example: British
          nullable: false
        address:
          properties:
            apartment_number:
              description: 'Apartment number'
              type: string
              example: Jon
              nullable: false
            address_1:
              description: 'Line/Address 1'
              type: string
              example: Jon
              nullable: false
            address_2:
              description: 'Line/Address 2'
              type: string
              example: F
              nullable: false
            city:
              description: City
              type: string
              example: Doe
              nullable: false
            state:
              description: State
              type: string
              example: Doe
              nullable: false
            postcode:
              description: Postcode
              type: string
              example: Doe
              nullable: false
          type: object
          nullable: false
        personal_number:
          description: 'Personal number'
          type: string
          example: '+44901299300111'
          nullable: false
      type: object
    RepresentativesResource:
      title: 'Representatives Resource'
      required:
        - id
        - profile_id
        - customer_reference
        - is_company
        - types
      properties:
        id:
          description: UUID
          type: string
          example: 4d532245-1ce2-4963-a61d-38b6163aed87
          nullable: false
        profile_id:
          description: 'Profile ID'
          type: string
          example: 4d532245-1ce2-4963-a61d-38b6163aed87
          nullable: false
        customer_reference:
          description: 'Unique custom reference number, which was set when profile was created.'
          type: string
          example: my-unique-code
          nullable: false
        is_company:
          description: 'Flag to determine if the person is a natural person or a company/legal entity.'
          type: boolean
          example: false
          nullable: false
        types:
          description: 'Type of the entity'
          type: array
          items:
            type: string
            enum:
              - director
              - identity
              - ownership
              - declaratory
              - principal
              - director_company
          example:
            - director
          nullable: false
        full_name:
          description: 'Full name of individual or legal/company name'
          type: string
          example: 'Jon Doe'
          nullable: true
        professional_profile:
          description: 'Professional profile'
          type: string
          example: 'https://professional-website.com'
          nullable: true
        country:
          description: 'Country Name'
          type: string
          example: 'United Kingdom of Great Britain and Northern Ireland'
          nullable: true
        country_code:
          description: 'Country Code'
          type: string
          example: GB
          nullable: true
        country_of_residence:
          description: 'Country of Residence'
          type: string
          example: 'United Kingdom of Great Britain and Northern Ireland'
          nullable: true
        residence_country_code:
          description: 'Residence country code'
          type: string
          example: GB
          nullable: true
        personal_number:
          description: 'Personal Number'
          type: string
          example: '+44071827199120'
          nullable: true
        first_name:
          description: 'First name'
          type: string
          example: Andre
          nullable: true
        last_name:
          description: 'Last name'
          type: string
          example: Ferraz
          nullable: true
        dob:
          description: 'Date of birth'
          type: string
          example: 10/01/2000
          nullable: true
        email:
          description: Email
          type: string
          example: <EMAIL>
          nullable: true
        job_title:
          description: 'Job title'
          type: string
          example: Engineer
          nullable: true
        apartment_number:
          description: 'Apartment Number'
          type: string
          example: '43'
          nullable: true
        address_1:
          description: 'Address line one'
          type: string
          example: '1 Main St'
          nullable: true
        address_2:
          description: 'Address line two'
          type: string
          example: null
          nullable: true
        city:
          description: City
          type: string
          example: London
          nullable: true
        state:
          description: State
          type: string
          example: California
          nullable: true
        post_code:
          description: 'Post code'
          type: string
          example: 'EC1 9A0'
          nullable: true
        country_of_birth:
          description: 'Country of birth'
          type: string
          example: 'United States of America'
          nullable: true
        birth_country_code:
          description: 'Country code of birth'
          type: string
          example: US
          nullable: true
        nationality:
          description: Nationality
          type: string
          example: British
          nullable: true
        person_type_details:
          description: 'Person type details'
          properties:
            appointment_date:
              description: 'Appointment date'
              type: string
              example: '2023-04-27 09:57:53'
              nullable: true
            majority:
              description: Majority
              type: boolean
              example: true
              nullable: true
            governing:
              description: Governing
              type: boolean
              example: true
              nullable: true
            responsibility:
              description: Responsibility
              type: boolean
              example: false
              nullable: true
            majority_percentage:
              description: 'Majority percentage'
              type: string
              example: '25'
              nullable: true
            specific_ownership_percentage:
              description: 'Specific ownership percentage'
              type: string
              example: '39'
              nullable: true
          type: object
          nullable: true
        copy_address:
          description: 'Copy Address'
          type: boolean
          example: false
          nullable: true
        updated_at:
          description: 'Updated at'
          type: string
          example: '2023-04-27 09:57:53'
          nullable: true
        created_at:
          description: 'Created at'
          type: string
          example: '2023-04-27 09:57:53'
          nullable: true
      type: object
    PersonVerificationsResourceResponse:
      title: 'Person Verifications Resource Response'
      required:
        - type
        - info
        - provider
      properties:
        type:
          description: 'The type of verification'
          type: string
          enum:
            - idv
            - id3
            - digital_footprint
          example: idv
          nullable: false
        provider:
          description: 'The provider for the given identification type'
          type: string
          enum:
            - id_scan
            - yoti
            - risk_seal
            - id3
          example: id_scan
          nullable: false
        info:
          description: 'Verification details'
          type: object
          example:
            name: 'John Doe'
            dob: '1990-01-01'
          nullable: false
      type: object
    CreateProfileRequest:
      title: 'Create Profile Request'
      required:
        - customer_reference
      properties:
        lookup_id:
          description: 'Search look up id'
          type: number
          example: 222278
          nullable: false
        response_id:
          description: 'Search response id'
          type: number
          example: 1
          nullable: false
        customer_reference:
          description: 'Unique reference to be assigned'
          type: string
          example: my-unique-reference
          nullable: false
        company_number:
          description: 'Company Registration Number'
          type: string
          example: '01299120'
          nullable: false
        name:
          description: 'Company name'
          type: string
          example: 'Alphabet inc'
          nullable: false
        vat_number:
          description: 'VAT Number'
          type: string
          example: '0129310'
          nullable: false
        domain:
          description: 'Company domain without protocol or path'
          type: string
          example: detected.co
          nullable: false
        address:
          required:
            - country_code
            - city
          properties:
            line_1:
              type: string
              example: 'Line 1 of London'
              nullable: false
            line_2:
              type: string
              example: 'Optional line of london street'
              nullable: false
            country_code:
              description: 'ISO 3166-1 alpha-2'
              type: string
              example: GB
              nullable: false
            town:
              type: string
              example: Byfleet
              nullable: false
            city:
              type: string
              example: Guilford
              nullable: false
            post_code:
              type: string
              example: 'G7AH 012'
              nullable: false
            province:
              type: string
              example: 'Greater London'
              nullable: false
            state:
              type: string
              example: California
              nullable: false
          type: object
          nullable: false
      type: object
    CreateProfileDomainRequest:
      title: 'Create Profile Domain Request'
      required:
        - domain
      properties:
        domain:
          description: 'Domain without any protocol or paths'
          type: string
          example: subdomain.example.com
          nullable: false
      type: object
    ProfileUpdateRequest:
      description: 'Profile update request'
      properties:
        assignee_id:
          description: 'Assign a user to a profile or unassign a user from a profile by setting the value `null`'
          type: string
          example: 83197bf-966b-43ae-ae52-541bcd0de607
          nullable: true
        risk_id:
          description: 'Assign a risk category to a profile or unassign a risk category from a profile by setting the value `null`'
          type: string
          example: 483197bf-966b-43ae-ae52-541bcd0de607
          nullable: true
        team_id:
          description: 'Assign a team to a profile or unassign a team from a profile by setting the value `null`'
          type: string
          example: 1
          nullable: true
        customer_reference:
          type: string
          example: GB-1299A
          nullable: false
        configuration_id:
          type: integer
          example: 1
          nullable: true
        review_status:
          type: string
          enum:
            - Processed
            - 'Needs Review'
            - 'Additional Info Required'
            - Approved
            - Declined
            - Archived
          example: Processed
          nullable: false
      type: object
    AssignProfileUserRequest:
      title: 'Assign Profile User Request'
      required:
        - user_id
      properties:
        user_id:
          description: 'User id'
          type: number
          example: 1202
          nullable: false
        role:
          description: 'Role user type'
          type: string
          enum:
            - admin
          example: admin
          nullable: false
      type: object
    ProfileListResource:
      title: 'Profile list Resource'
      required:
        - id
        - customer_reference
        - review_status
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: string
          example: a0b7b54a-d79c-458c-bf79-83cf2e4d65af
          nullable: false
        customer_reference:
          description: 'Customer Reference'
          type: string
          example: my-reference-code
          nullable: false
        company_name:
          description: 'Company Name'
          type: string
          example: 'My Company'
          nullable: false
        risk:
          description: Risk
          properties:
            id:
              description: id
              type: string
              example: ********-6843-401d-9283-532fb4350b4f
              nullable: false
            label:
              description: Label
              type: string
              example: 'P2 review required'
              nullable: true
            category:
              description: Category
              type: string
              example: Medium
              nullable: true
          type: object
          nullable: true
        team:
          description: Team
          properties:
            id:
              description: id
              type: number
              example: 10
              nullable: false
            label:
              description: Label
              type: string
              example: 'The A Team - edited (Compliance'
              nullable: true
          type: object
          nullable: true
        configuration:
          description: Configuration
          properties:
            id:
              description: id
              type: number
              example: 10
              nullable: false
            name:
              description: name
              type: string
              example: Default
              nullable: true
          type: object
          nullable: true
        review_status:
          type: string
          enum:
            - Processed
            - 'Needs Review'
            - 'Additional Info Required'
            - Approved
            - Declined
            - Archived
          example: Processed
          nullable: false
        assignee:
          description: Assignee
          properties:
            id:
              description: id
              type: string
              example: auth0|60dc8a229d13ba006c5d51ab
              nullable: false
            name:
              description: Name
              type: string
              example: Jonathan
              nullable: true
          type: object
          nullable: true
        review_at:
          description: 'Review at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    ProfileDomainResource:
      title: 'Profile Domain Resource'
      required:
        - id
        - type
        - domain
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: string
          example: a0b7b54a-d79c-458c-bf79-83cf2e4d65af
          nullable: false
        type:
          description: 'type can be portal or api_import'
          type: string
          nullable: false
        domain:
          description: domain
          type: string
          nullable: false
        whois:
          description: 'Details about the WHOIS information of the domain'
          properties:
            status:
              description: 'WHOIS status of the domain'
              type: string
              example: OK
              nullable: true
            age:
              description: 'Human readable age of domain'
              type: string
              example: '9 years, 7 months and 1 week'
              nullable: true
            ageDays:
              description: 'Age of the domain in days'
              type: integer
              example: 3505
              nullable: true
            createdAt:
              description: 'Creation date of the domain'
              type: string
              format: date-time
              example: '2015-07-01T05:19:10Z'
              nullable: true
            updatedAt:
              description: 'Last updated date of domain WHOIS details'
              type: string
              format: date-time
              example: '2024-04-23T09:02:40Z'
              nullable: true
            expiresAt:
              description: 'Expiry date of the domain registration'
              type: string
              format: date-time
              example: '2032-07-01T05:19:10Z'
              nullable: true
          type: object
          nullable: true
        social:
          description: 'Details about the social media presence of the company'
          properties:
            title:
              description: "Title representing the company's online presence"
              type: string
              example: 'Digital trust in Banking using Artificial Intelligence and Blockchain'
              nullable: true
            description:
              description: "Description about the company's objective or online presence"
              type: string
              example: 'Company helps financial institutions make their digital regulatory processes simple, secure and compliant. This is achieved by providing human like fraud detection using Artificial Intelligence and Blockchain'
              nullable: true
            responded:
              description: 'Indicates if the website was active during the crawling.'
              type: boolean
              example: true
              nullable: true
            twitter:
              description: 'Twitter profile URL of the company'
              type: string
              example: ''
              nullable: true
            facebook:
              description: 'Facebook profile URL of the company'
              type: string
              example: 'https://www.facebook.com/ExampleCompany/'
              nullable: true
            linkedIn:
              description: 'LinkedIn profile URL of the company'
              type: string
              example: 'https://www.linkedin.com/company/example-com'
              nullable: true
            instagram:
              description: 'Instagram profile URL of the company'
              type: string
              example: ''
              nullable: true
          type: object
          nullable: true
        online_presence:
          description: 'Online presence'
          type: array
          items:
            properties:
              founding_year:
                description: 'Year the company was founded'
                type: string
                example: '2015'
                nullable: true
              phone_number:
                description: 'A contact phone number for the company'
                type: string
                example: '******-456-7890'
                nullable: true
              industry:
                description: 'The industry of the company'
                type: string
                example: 'FINANCIAL SERVICES'
                nullable: true
              tags:
                description: 'Tags describing the company'
                type: array
                items:
                  type: string
                  example: B2B
                nullable: true
              address:
                description: 'The company address'
                type: string
                example: 'ONE LIBERTY PLAZA, 165 BROADWAY 23RD FLOOR, NEW YORK CITY, NEW YORK 10006, US'
                nullable: true
            type: object
          nullable: true
        scam_advisor:
          description: 'Scam advisor assessment details for the given domain'
          properties:
            score:
              description: 'The actual Trust Score of the Domain (0 = very likely a scam, 100 = very likely legit).'
              type: integer
              example: 100
              nullable: true
            blacklisted:
              description: 'Whether or not the domain is blacklisted.'
              type: boolean
              example: false
              nullable: true
            dns_threat_markers:
              description: 'Number of DNS services identifying the domain as containing malicious (threat) content.'
              type: integer
              example: 0
              nullable: true
            dns_adult_content_markers:
              description: 'Number of DNS services identifying the domain as containing adult content.'
              type: integer
              example: 0
              nullable: true
            website_title:
              description: 'The meta-title of the website obtained by the scam advisor.'
              type: string
              example: 'Amazon.com: Online Shopping for Electronics...'
              nullable: true
            website_description:
              description: 'The meta-description of the website retrieved by the scam advisor.'
              type: string
              example: 'Free delivery on millions of items with Prime...'
              nullable: true
            website_keywords:
              description: 'Meta-keywords defined for the website.'
              type: array
              items:
                type: string
                example: Amazon
              nullable: true
            block_search_engines:
              description: 'Whether the domain has a robots.txt that blocks search engines from indexing the site.'
              type: boolean
              example: false
              nullable: true
            reviews:
              description: 'List of reviews for the domain from various sources.'
              type: array
              items:
                properties:
                  source: { description: 'The platform from which the review is sourced (e.g., Trustpilot).', type: string, example: Trustpilot, nullable: true }
                  source_url: { description: 'URL to the review source.', type: string, format: url, example: 'https://www.trustpilot.com/review/amazon.com', nullable: true }
                  count: { description: 'The total number of available reviews for this domain from the review source.', type: integer, example: 12920, nullable: true }
                  avg_score: { description: 'The average review score (normalized between 1 [very bad] and 5 [very good]).', type: number, format: float, example: 2.2, nullable: true }
                type: object
              nullable: true
            gca_domain_trust:
              description: 'Assessment details for Global Cyber Alliance domain trust metrics.'
              properties:
                type:
                  description: 'The type of rating assigned (e.g., C = Third parties like Scamadviser/Spamhaus).'
                  type: string
                  example: C
                  nullable: true
                activity:
                  description: 'Domain activity status (A = active site, S = suspended, N = non-existing).'
                  type: string
                  example: A
                  nullable: true
                provider:
                  description: 'The name of the provider reporting the domain trust analysis.'
                  type: string
                  example: ScamAdviser
                  nullable: true
                created_at:
                  description: 'The timestamp when the trust data was initially provided.'
                  type: string
                  format: date-time
                  example: '2021-03-24T12:00:17Z'
                  nullable: true
                is_blocked:
                  description: "Indicates if the domain's trust level is blocked."
                  type: boolean
                  example: false
                  nullable: true
                updated_at:
                  description: 'The timestamp when the trust data was last updated.'
                  type: string
                  format: date-time
                  example: '2022-03-03T16:21:46.565251Z'
                  nullable: true
                source_name:
                  description: 'The source of the trust data (e.g., ScamAdviser).'
                  type: string
                  example: scamadviser.com
                  nullable: true
                provider_role:
                  description: 'The role of the provider (e.g., registry, registrar, or other).'
                  type: string
                  example: other
                  nullable: true
                classification:
                  description: 'Classification level of trustworthiness (e.g., 1 = Definitely legitimate).'
                  type: integer
                  example: 1
                  nullable: true
                external_source:
                  description: 'Indicates if the data originates from an external source.'
                  type: boolean
                  example: false
                  nullable: true
                provider_rating:
                  description: "The internal rating of the provider's reliability."
                  type: string
                  example: C
                  nullable: true
              type: object
              nullable: true
            antiphishing:
              description: 'Domain data from the eCrime Exchange.'
              properties:
                count:
                  description: 'The count of sources that flagged the domain as being used for phishing.'
                  type: integer
                  example: 0
                  nullable: true
              type: object
              nullable: true
            certnz:
              description: 'CERT NZ marker information.'
              properties:
                labels:
                  description: 'Labels describing the possible threat(s).'
                  type: array
                  items: { type: string }
                  nullable: true
                marking:
                  description: 'Classification provided by CERT NZ (e.g., amber, red).'
                  type: string
                  example: amber
                  nullable: true
              type: object
              nullable: true
            maltiverse:
              description: 'Maltiverse classification and threat tags for the domain.'
              properties:
                tags:
                  description: 'List of threat tags identified by Maltiverse.'
                  type: array
                  items: { type: string, example: bokbot }
                  nullable: true
                classification:
                  description: 'Domain threat classification by Maltiverse.'
                  type: string
                  example: malicious
                  nullable: true
              type: object
              nullable: true
            ipqs:
              description: 'IPQS domain analysis results.'
              properties:
                risk_score:
                  description: 'Overall risk score assigned by IPQS to the domain.'
                  type: integer
                  example: 85
                  nullable: true
                unsafe:
                  description: 'Indicates if the domain is flagged as unsafe by IPQS.'
                  type: boolean
                  example: true
                  nullable: true
                dns_valid:
                  description: 'Whether or not IPQS considers the DNS of the domain valid.'
                  type: boolean
                  example: true
                  nullable: true
                parking:
                  description: 'Whether the domain is identified as parked.'
                  type: boolean
                  example: false
                  nullable: true
                spamming:
                  description: 'Indicates if significant spam activity originates from the domain.'
                  type: boolean
                  example: true
                  nullable: true
                malware:
                  description: 'Indicates if malware distribution has been detected for the domain.'
                  type: boolean
                  example: false
                  nullable: true
                phishing:
                  description: 'Indicates if phishing attempts are associated with the domain.'
                  type: boolean
                  example: false
                  nullable: true
                suspicious:
                  description: 'Identifies suspicious activity associated with the domain.'
                  type: boolean
                  example: true
                  nullable: true
                adult:
                  description: 'Flags if adult content is hosted on the domain.'
                  type: boolean
                  example: false
                  nullable: true
                domain_age:
                  description: 'The age of the domain in timestamp form.'
                  type: integer
                  example: 1609459200
                  nullable: true
              type: object
              nullable: true
            created_at:
              description: 'Timestamp when this record was created.'
              type: string
              format: date-time
              example: '2020-09-01 13:32:16'
              nullable: true
            updated_at:
              description: 'Timestamp when this record was last updated.'
              type: string
              format: date-time
              example: '2020-10-07 08:00:11'
              nullable: true
          type: object
          nullable: true
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    ProfileResource:
      title: 'Profile Resource'
      required:
        - id
        - customer_reference
        - review_status
        - updated_at
        - created_at
      type: object
      allOf:
        -
          $ref: '#/components/schemas/ProfileListResource'
        -
          properties:
            id:
              description: id
              type: string
              example: a0b7b54a-d79c-458c-bf79-83cf2e4d65af
              nullable: false
            customer_reference:
              description: 'Customer Reference'
              type: string
              example: my-reference-code
              nullable: false
            risk:
              description: Risk
              properties:
                id:
                  description: id
                  type: string
                  example: ********-6843-401d-9283-532fb4350b4f
                  nullable: false
                label:
                  description: Label
                  type: string
                  example: 'P2 review required'
                  nullable: true
                category:
                  description: Category
                  type: string
                  example: Medium
                  nullable: true
              type: object
              nullable: true
            team:
              description: Team
              properties:
                id:
                  description: id
                  type: number
                  example: 10
                  nullable: false
                label:
                  description: Label
                  type: string
                  example: 'The A Team - edited (Compliance'
                  nullable: true
              type: object
              nullable: true
            configuration:
              description: Configuration
              properties:
                id:
                  description: id
                  type: number
                  example: 10
                  nullable: false
                name:
                  description: name
                  type: string
                  example: Default
                  nullable: true
              type: object
              nullable: true
            review_status:
              type: string
              enum:
                - Processed
                - 'Needs Review'
                - 'Additional Info Required'
                - Approved
                - Declined
                - Archived
              example: Processed
              nullable: false
            onboarding_statuses:
              description: 'Portal onboarding statuses'
              type: array
              items:
                properties:
                  title: { description: 'Onboarding step name', type: string, example: 'Declare directors', nullable: false }
                  status: { description: 'Status of onboarding step', type: string, enum: [pending, completed, in-progress, in-complete], example: pending, nullable: false }
                type: object
              nullable: true
            assignee:
              description: Assignee
              properties:
                id:
                  description: id
                  type: string
                  example: auth0|60dc8a229d13ba006c5d51ab
                  nullable: false
                name:
                  description: Name
                  type: string
                  example: Jonathan
                  nullable: true
              type: object
              nullable: true
            review_at:
              description: 'Review at'
              type: string
              example: '2024-07-16 14:52:33'
              nullable: false
            company:
              description: Company
              properties:
                profile:
                  description: 'Company profile'
                  properties: { ProfileCompanyName: { description: ProfileCompanyName, type: string, nullable: true }, ProfileCompanyStatusActive: { description: ProfileCompanyStatusActive, type: boolean, example: true, nullable: true }, ProfileCompanyRegisteredCountry: { description: ProfileCompanyRegisteredCountry, type: string, nullable: true }, ProfileCompanyReference: { description: ProfileCompanyReference, type: string, nullable: true }, ProfileCompanyStatusProvider: { description: ProfileCompanyStatusProvider, type: string, nullable: true }, ProfileCompanyLegalForm: { description: ProfileCompanyLegalForm, type: string, nullable: true }, ProfileCompanyRegistrationDate: { description: ProfileCompanyRegistrationDate, type: string, nullable: true }, ProfileCompanyDataSource: { description: ProfileCompanyDataSource, type: string, enum: [bolddata, creditsafe, enigma, globaldatabase, manual, nuna, opencorporates, orbis, soletrader], nullable: true }, ProfileCompanyVatNumber: { description: ProfileCompanyVatNumber, type: string, nullable: true }, ProfileCompanyHasVatNumber: { description: ProfileCompanyHasVatNumber, type: boolean, example: false, nullable: true }, ProfileCompanyNumOfEmployees: { description: ProfileCompanyNumOfEmployees, type: number, example: 2, nullable: true }, ProfileCompanyPhoneNumber: { description: ProfileCompanyPhoneNumber, type: string, nullable: true }, ProfileCompanyEmail: { description: ProfileCompanyEmail, type: string, nullable: true }, ProfileCompanyIsExporter: { description: ProfileCompanyIsExporter, type: boolean, example: true, nullable: true }, ProfileCompanyIsImporter: { description: ProfileCompanyIsImporter, type: boolean, example: true, nullable: true }, ProfileCompanyRegisteredAddress: { description: ProfileCompanyRegisteredAddress, type: string, nullable: true }, ProfileCompanyRegisteredAddress1: { description: ProfileCompanyRegisteredAddress1, type: string, nullable: true }, ProfileCompanyRegisteredAddress2: { description: ProfileCompanyRegisteredAddress2, type: string, nullable: true }, ProfileCompanyRegisteredAddressCity: { description: ProfileCompanyRegisteredAddressCity, type: string, nullable: true }, ProfileCompanyRegisteredAddressProvince: { description: ProfileCompanyRegisteredAddressProvince, type: string, nullable: true }, ProfileCompanyRegisteredAddressPostCode: { description: ProfileCompanyRegisteredAddressPostCode, type: string, nullable: true }, ProfileCompanyRegisteredAddressCountryCode: { description: ProfileCompanyRegisteredAddressCountryCode, type: string, nullable: true }, ProfileCompanyCapital: { description: ProfileCompanyCapital, type: number, nullable: true }, ProfileCompanyCurrency: { description: ProfileCompanyCurrency, type: string, nullable: true }, ProfileCompanyCurrentAssets: { description: ProfileCompanyCurrentAssets, type: number, nullable: true }, ProfileCompanyEBITDA: { description: ProfileCompanyEBITDA, type: string, nullable: true }, ProfileCompanyGrossProfit: { description: ProfileCompanyGrossProfit, type: number, nullable: true }, ProfileCompanyLastAccountsDate: { description: ProfileCompanyLastAccountsDate, type: string, nullable: true }, ProfileCompanyLongTermDebt: { description: ProfileCompanyLongTermDebt, type: number, nullable: true }, ProfileCompanyTotalAssets: { description: ProfileCompanyTotalAssets, type: number, nullable: true }, ProfileCompanyTurnover: { description: ProfileCompanyTurnover, type: number, nullable: true }, ProfileCompanyIndustryClassifications: { description: ProfileCompanyIndustryClassifications, type: array, items: {  }, example: ['******** - CATALOG,MAIL HOUSES', '5961 - Ret mail-order house'], nullable: true }, ProfileCompanyIndustryCodes: { description: ProfileCompanyIndustryCodes, type: array, items: {  }, example: ['******** - CATALOG,MAIL HOUSES', '5961 - Ret mail-order house'], nullable: true }, ProfileCompanyIndustryDescriptions: { description: ProfileCompanyIndustryDescriptions, type: array, items: {  }, example: [], nullable: true }, ProfileCompanyCreditProbabilityOfDefault: { description: ProfileCompanyCreditProbabilityOfDefault, type: number, nullable: true }, ProfileCompanyCreditRatingDate: { description: ProfileCompanyCreditRatingDate, type: string, nullable: true }, ProfileCompanyCreditContractLimit: { description: ProfileCompanyCreditContractLimit, type: number, nullable: true }, ProfileCompanyCreditRating: { description: ProfileCompanyCreditRating, type: string, nullable: true }, ProfileCompanyCreditRatingDescription: { description: ProfileCompanyCreditRatingDescription, type: string, nullable: true }, ProfileCompanyCreditLimit: { description: ProfileCompanyCreditLimit, type: number, nullable: true }, ProfileCompanyCreditProviderMax: { description: ProfileCompanyCreditProviderMax, type: number, nullable: true }, ProfileCompanyCreditProviderMin: { description: ProfileCompanyCreditProviderMin, type: number, nullable: true }, ProfileCompanyCreditProviderValue: { description: ProfileCompanyCreditProviderValue, type: number, nullable: true }, ProfileCompanyCreditProviderDescription: { description: ProfileCompanyCreditProviderDescription, type: string, nullable: true }, ProfileCompanyCreditPreviousRating: { description: ProfileCompanyCreditPreviousRating, type: string, nullable: true }, ProfileCompanyCreditPreviousRatingDescription: { description: ProfileCompanyCreditPreviousRatingDescription, type: string, nullable: true }, ProfileCompanyCreditPreviousLimit: { description: ProfileCompanyCreditPreviousLimit, type: number, nullable: true }, ProfileCompanyCreditPreviousProviderMax: { description: ProfileCompanyCreditPreviousProviderMax, type: number, nullable: true }, ProfileCompanyCreditPreviousProviderMin: { description: ProfileCompanyCreditPreviousProviderMin, type: number, nullable: true }, ProfileCompanyCreditPreviousProviderValue: { description: ProfileCompanyCreditPreviousProviderValue, type: number, nullable: true }, ProfileCompanyCreditPreviousProviderDescription: { description: ProfileCompanyCreditPreviousProviderDescription, type: string, nullable: true } }
                  type: object
                  nullable: true
                director:
                  description: 'List of data source directors'
                  properties: { data: { description: Data, type: array, items: { properties: { director_id: { description: 'Director id', type: string, nullable: true }, job_title: { description: 'Job title', type: string, nullable: true }, gender: { description: Gender, type: string, nullable: true }, appointment_date: { description: 'Appointment date', type: string, nullable: true }, resignation_date: { description: 'Resignation date', type: string, nullable: true }, dob: { description: 'Date of birth', type: string, nullable: true }, address: { description: Address, type: string, nullable: true }, country: { description: country, type: string, nullable: true }, nationality: { description: Nationality, type: string, nullable: true }, company_number: { description: 'Company number', type: string, nullable: true }, screenings: { description: Screenings, type: array, items: {  }, nullable: true }, is_active: { description: 'Is Active', type: boolean, nullable: true }, suffix: { description: Suffix, type: string, nullable: true }, salutation: { description: Salutation, type: string, nullable: true }, shareholder: { description: shareholder, type: string, nullable: true }, position_type: { description: 'Position type', type: string, nullable: true }, responsibility: { description: Responsibility, type: string, nullable: true }, type: { description: Type, type: string, nullable: true }, title: { description: Title, type: string, nullable: true }, full_name: { description: 'Full name', type: string, nullable: true }, first_name: { description: 'First name', type: string, nullable: true }, last_name: { description: 'Last name', type: string, nullable: true }, middle_name: { description: 'Middle name', type: string, nullable: true } }, type: object }, nullable: false }, summary: { description: 'Summary of directors', properties: { total_count: { description: 'Total count', type: number, nullable: true }, active_count: { description: 'Active count', type: number, nullable: true }, inactive_count: { description: 'Innactive count', type: number, nullable: true } }, type: object } }
                  type: object
                share_structure:
                  description: 'Details of the share structure including shareholders'
                  properties: { data: { description: Data, properties: { share_holders: { description: 'List of shareholders', type: array, items: { properties: { id: { description: id, type: string, nullable: true }, name: { description: name, type: string, nullable: true }, currency: { description: currency, type: string, nullable: true }, share_type: { description: share_type, type: string, nullable: true }, share_types: { description: share_types, type: array, items: { properties: { currency: { description: currency, type: string, nullable: true }, share_type: { description: share_type, type: string, nullable: true }, value_of_shares: { description: value_of_shares, type: number, nullable: true }, value_per_share: { description: value_per_share, type: number, nullable: true }, number_of_shares: { description: number_of_shares, type: number, nullable: true }, company_reference: { description: company_reference, type: string, nullable: true } }, type: object }, nullable: true }, value_of_shares: { description: value_of_shares, type: number, nullable: true }, number_of_shares: { description: number_of_shares, type: number, nullable: true }, shareholder_type: { description: shareholder_type, type: string, nullable: true }, percentage_shares_held: { description: percentage_shares_held, type: number, nullable: true }, address: { description: address, type: number, nullable: true } }, type: object } } }, type: object }, issued_shares_total: { description: issued_shares_total, type: number, nullable: true }, issued_shares_capital_value: { description: issued_shares_capital_value, type: number, nullable: true } }
                  type: object
                structure:
                  description: 'Structure of company'
                  properties: { ultimate_parent: { description: 'Ultimate parent', properties: { provider_id: { description: provider_id, type: string, nullable: true }, name: { description: name, type: string, nullable: true }, country_code: { description: country_code, type: string, nullable: true }, type: { description: type, type: string, nullable: true }, status: { description: status, type: string, nullable: true }, company_reference: { description: company_reference, type: string, nullable: true }, address: { description: address, type: string, nullable: true } }, type: object }, immediate_parent: { description: 'Immediate parent', properties: { provider_id: { description: provider_id, type: string, nullable: true }, name: { description: name, type: string, nullable: true }, country_code: { description: country_code, type: string, nullable: true }, type: { description: type, type: string, nullable: true }, status: { description: status, type: string, nullable: true }, company_reference: { description: company_reference, type: string, nullable: true }, address: { description: address, type: string, nullable: true } }, type: object } }
                  type: object
                financials:
                  description: 'Financials of company'
                  properties: { data: { description: Data, type: array, items: { properties: { ratios: { description: ratios, properties: { gearing: { description: gearing, type: number, nullable: true }, debtor_days: { description: debtor_days, type: number, nullable: true }, creditor_days: { description: creditor_days, type: number, nullable: true }, current_ratio: { description: current_ratio, type: number, nullable: true }, total_debt_ratio: { description: total_debt_ratio, type: number, nullable: true }, current_debt_ratio: { description: current_debt_ratio, type: number, nullable: true }, equity_in_percentage: { description: equity_in_percentage, type: number, nullable: true }, stock_turnover_ratio: { description: stock_turnover_ratio, type: number, nullable: true }, pre_tax_profit_margin: { description: pre_tax_profit_margin, type: number, nullable: true }, return_on_capital_employed: { description: return_on_capital_employed, type: number, nullable: true }, liquidity_ratio_or_acid_test: { description: liquidity_ratio_or_acid_test, type: number, nullable: true }, sales_or_net_working_capital: { description: sales_or_net_working_capital, type: number, nullable: true }, return_on_net_assets_employed: { description: return_on_net_assets_employed, type: number, nullable: true }, return_on_total_assets_employed: { description: return_on_total_assets_employed, type: number, nullable: true } }, type: object }, general: { description: general, properties: { type: { description: type, type: string, nullable: true }, currency: { description: currency, type: string, nullable: true }, year_end_date: { description: year_end_date, type: string, nullable: true }, number_of_weeks: { description: number_of_weeks, type: number, nullable: true }, consolidated_accounts: { description: consolidated_accounts, type: boolean, nullable: true } }, type: object }, balance_sheet: { description: general, properties: { cash: { description: cash, type: number, nullable: true }, total_assets: { description: total_assets, type: number, nullable: true }, other_reserves: { description: other_reserves, type: number, nullable: true }, trade_payables: { description: trade_payables, type: number, nullable: true }, bank_liabilities: { description: bank_liabilities, type: number, nullable: true }, revenue_reserves: { description: revenue_reserves, type: number, nullable: true }, total_inventories: { description: total_inventories, type: number, nullable: true }, total_liabilities: { description: total_liabilities, type: number, nullable: true }, total_receivables: { description: total_receivables, type: number, nullable: true }, trade_receivables: { description: trade_receivables, type: number, nullable: true }, total_fixed_assets: { description: total_fixed_assets, type: number, nullable: true }, other_current_assets: { description: other_current_assets, type: number, nullable: true }, total_current_assets: { description: total_current_assets, type: number, nullable: true }, total_tangible_assets: { description: total_tangible_assets, type: number, nullable: true }, other_loans_or_finance: { description: other_loans_or_finance, type: number, nullable: true }, called_up_share_capital: { description: called_up_share_capital, type: number, nullable: true }, total_intangible_assets: { description: total_intangible_assets, type: number, nullable: true }, total_other_fixed_assets: { description: total_other_fixed_assets, type: number, nullable: true }, miscellaneous_liabilities: { description: miscellaneous_liabilities, type: number, nullable: true }, miscellaneous_receivables: { description: miscellaneous_receivables, type: number, nullable: true }, total_current_liabilities: { description: total_current_liabilities, type: number, nullable: true }, total_shareholders_equity: { description: total_shareholders_equity, type: number, nullable: true }, total_long_term_liabilities: { description: total_long_term_liabilities, type: number, nullable: true }, bank_liabilities_due_after1year: { description: bank_liabilities_due_after1year, type: number, nullable: true }, other_loans_or_finance_due_after1year: { description: other_loans_or_finance_due_after1year, type: number, nullable: true }, miscellaneous_liabilities_due_after1year: { description: miscellaneous_liabilities_due_after1year, type: number, nullable: true } }, type: object }, profit_and_loss: { description: profit_and_loss, properties: { tax: { description: tax, type: number, nullable: true }, revenue: { description: revenue, type: number, nullable: true }, dividends: { description: dividends, type: number, nullable: true }, amortisation: { description: amortisation, type: number, nullable: true }, depreciation: { description: depreciation, type: number, nullable: true }, pension_costs: { description: pension_costs, type: number, nullable: true }, operating_costs: { description: operating_costs, type: number, nullable: true }, retained_profit: { description: retained_profit, type: number, nullable: true }, operating_profit: { description: operating_profit, type: number, nullable: true }, profit_after_tax: { description: profit_after_tax, type: number, nullable: true }, profit_before_tax: { description: profit_before_tax, type: number, nullable: true }, financial_expenses: { description: financial_expenses, type: number, nullable: true }, minority_interests: { description: minority_interests, type: number, nullable: true }, wages_and_salaries: { description: wages_and_salaries, type: number, nullable: true }, other_appropriations: { description: other_appropriations, type: number, nullable: true } }, type: object }, other_financials: { description: other_financials, properties: { net_worth: { description: net_worth, type: number, nullable: true }, working_capital: { description: working_capital, type: number, nullable: true }, contingent_liabilities: { description: contingent_liabilities, type: string, nullable: true } }, type: object } }, type: object } } }
                  type: object
                psc:
                  description: 'People with significant control'
                  type: array
                  items: { properties: { nature_of_control: { description: nature_of_control, type: array, items: {  }, nullable: true }, is_active: { description: is_active, type: boolean, nullable: true }, name: { description: name, type: string, nullable: true }, kind: { description: kind, type: string, nullable: true }, entityType: { description: entityType, type: string, nullable: true }, address: { description: address, type: string, nullable: true }, notified_on: { description: notified_on, type: string, nullable: true }, ceased_on: { description: ceased_on, type: string, nullable: true }, dob_day: { description: dob_day, type: number, nullable: true }, dob_month: { description: dob_month, type: number, nullable: true }, dob_year: { description: dob_year, type: number, nullable: true } }, type: object }
                domain:
                  description: 'Company domain information'
                  type: array
                  items: { properties: { domain: { description: domain, type: string, nullable: false }, online_presence: { description: 'Online presence', type: array, items: { properties: { founding_year: { description: 'Year the company was founded', type: string, example: '2015', nullable: true }, phone_number: { description: 'A contact phone number for the company', type: string, example: '******-456-7890', nullable: true }, industry: { description: 'The industry of the company', type: string, example: 'FINANCIAL SERVICES', nullable: true }, tags: { description: 'Tags describing the company', type: array, items: { type: string, example: B2B }, nullable: true }, address: { description: 'The company address', type: string, example: 'ONE LIBERTY PLAZA, 165 BROADWAY 23RD FLOOR, NEW YORK CITY, NEW YORK 10006, US', nullable: true } }, type: object }, nullable: true } }, type: object }
                screenings:
                  description: Screenings
                  type: array
                  items: {  }
                  example: []
                  nullable: true
              type: object
              nullable: true
            updated_at:
              description: 'Updated at'
              type: string
              example: '2024-07-16 14:52:33'
              nullable: false
            created_at:
              description: 'Created at'
              type: string
              example: '2024-07-16 14:52:33'
              nullable: false
          type: object
    SearchRequest:
      title: 'Search Request'
      required:
        - type
        - name
        - address
      properties:
        type:
          description: 'Entity type to be searched'
          type: string
          enum:
            - company
          example: company
          nullable: false
        name:
          description: 'Company name'
          type: string
          example: 'Detected LTD'
          nullable: false
        vat_number:
          description: VAT
          type: string
          example: GB123456789
          nullable: false
        company_reference:
          description: 'Unique string value for company profile to be imported.'
          type: string
          example: 12ASJ1
          nullable: false
        address:
          required:
            - country_code
            - city
          properties:
            line_1:
              type: string
              example: 'Line 1 of London'
              nullable: false
            line_2:
              type: string
              example: 'Optional line of london street'
              nullable: false
            country_code:
              description: 'ISO 3166-1 alpha-2'
              type: string
              example: GB
              nullable: false
            town:
              type: string
              example: Byfleet
              nullable: false
            city:
              type: string
              example: Guilford
              nullable: false
            post_code:
              type: string
              example: 'G7AH 012'
              nullable: false
            province:
              type: string
              example: 'Greater London'
              nullable: false
            state:
              type: string
              example: California
              nullable: false
          type: object
          nullable: false
      type: object
    SearchResponse:
      title: 'Search Response'
      properties:
        data:
          description: 'List of search results'
          type: array
          items:
            properties:
              type:
                description: 'Type of search returned'
                type: string
                enum:
                  - company
                example: company
              name:
                description: 'Company name'
                type: string
                example: 'Detected LTD'
              company_reference:
                description: 'Company Reference'
                type: string
                example: '03350657'
              provider:
                description: 'Provider source'
                type: string
                example: opencorporates
              provider_reference:
                description: 'Provider Reference'
                type: string
                example: '03350657'
              jurisdiction_code:
                description: 'Country code'
                type: string
                example: gb
              company_type:
                description: 'Company type e.g private, sole trader etc'
                type: string
                example: 'PRIVATE LIMITED COMPANY'
              current_status:
                description: 'Status of company'
                type: string
                example: Dissolved
              address:
                description: 'Company Address'
                type: string
                example: '28 Road Blocks, RB0 9AJ'
              vat_number:
                description: ''
                type: string
                example: '9128391'
                nullable: true
              province_code:
                description: ''
                type: string
                example: I0
                nullable: true
              post_code:
                description: 'Company Address Postcode'
                type: string
                example: 'RB0 9AJ'
              city:
                description: 'Company Address City'
                type: string
                example: London
              confidence:
                description: ''
                type: integer
                example: 100
              response_id:
                description: ''
                type: integer
                example: 1
            type: object
        status:
          description: 'Status of search'
          type: string
          enum:
            - EMPTY
            - PARTIAL
            - MATCH
          example: MATCH
        lookup_id:
          description: 'Lookup id'
          type: integer
          example: 717
      type: object
    PortalUserCreateRequest:
      title: 'User create request'
      required:
        - email
        - type
      properties:
        email:
          description: Email
          type: string
          example: <EMAIL>
          nullable: false
      type: object
    ProfilePortalUserActivityResponse:
      title: 'Profile Portal User Activity Resource'
      required:
        - id
        - type
        - ip
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: number
          example: 1209
          nullable: false
        type:
          description: 'Type of activity'
          type: string
          enum:
            - access_log
          example: access_log
          nullable: false
        user:
          description: 'Portal user information'
          required:
            - id
            - email
          properties:
            id:
              description: id
              type: number
              example: 10
              nullable: false
            name:
              description: name
              type: string
              example: 'Jon Doe'
              nullable: true
            email:
              description: email
              type: string
              example: <EMAIL>
              nullable: false
          type: object
          nullable: true
        ip:
          description: 'User iP'
          type: string
          example: 912.30.217.123
          nullable: false
        browser_agent:
          description: 'Browser agent'
          type: string
          example: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
          nullable: true
        session_information:
          description: 'Portal user session information'
          properties:
            city:
              description: city
              type: string
              example: London
              nullable: true
            region:
              description: region
              type: string
              example: England
              nullable: true
            country_code:
              description: 'Country code'
              type: string
              example: GB
              nullable: true
            latitude:
              description: Latitude
              type: string
              example: '51.5085'
              nullable: true
            longitude:
              description: Longitude
              type: string
              example: '-0.1257'
              nullable: true
            time_zone:
              description: 'Time zone'
              type: string
              example: Europe/London
              nullable: true
            privacy_node:
              description: 'Session privacy information'
              properties:
                tor:
                  description: 'Flags whether or not portal user is using tor browser'
                  type: boolean
                  example: false
                  nullable: false
                vpn:
                  description: 'Flags whether or not portal user is a vpn'
                  type: boolean
                  example: false
                  nullable: false
                proxy:
                  description: 'Flags whether or not portal user is a proxy'
                  type: boolean
                  example: false
                  nullable: false
                relay:
                  description: 'Flags whether or not portal user is using a relay'
                  type: boolean
                  example: false
                  nullable: false
                hosting:
                  description: hosting
                  type: boolean
                  example: false
                  nullable: false
                service:
                  description: 'Name of service portal is using, e.g can be a VPN, proxy service etc'
                  type: string
                  example: null
                  nullable: true
              type: object
              nullable: true
          type: object
          nullable: true
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    PortalUserViewResource:
      title: 'Portal User View Resource'
      required:
        - id
        - email
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: integer
          example: 10291
          nullable: false
        name:
          description: Name
          type: string
          example: Jonathan
          nullable: true
        email:
          description: Email
          type: string
          example: <EMAIL>
          nullable: false
        profiles:
          description: 'List profile which user is assigned to'
          type: array
          items:
            required:
              - id
              - name
              - role
            properties:
              id:
                description: 'Profile id'
                type: string
                example: 01029301n-12312091-12
                nullable: false
              name:
                description: 'Name of profile'
                type: string
                example: 'Financial ltd'
                nullable: false
              role:
                description: 'Role of user in this profile'
                type: string
                enum:
                  - admin
                  - identity
                example: admin
                nullable: false
            type: object
          nullable: true
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    UserListResource:
      title: 'User List Resource'
      required:
        - id
        - name
        - email
        - blocked
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: string
          example: auth0|60dc8a229d13ba006c5d51ab
          nullable: false
        name:
          description: Name
          type: string
          example: Jonathan
          nullable: false
        email:
          description: Email
          type: string
          example: <EMAIL>
          nullable: false
        blocked:
          description: 'Flag to determine if user is blocked'
          type: boolean
          example: true
          nullable: false
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
    UserViewResource:
      title: 'User View Resource'
      required:
        - id
        - name
        - email
        - blocked
        - updated_at
        - created_at
      properties:
        id:
          description: id
          type: string
          example: auth0|60dc8a229d13ba006c5d51ab
          nullable: false
        name:
          description: Name
          type: string
          example: Jonathan
          nullable: false
        email:
          description: Email
          type: string
          example: <EMAIL>
          nullable: false
        blocked:
          description: 'Flag to determine if user is blocked'
          type: boolean
          example: true
          nullable: false
        teams:
          description: 'Teams in which user is assigned to'
          type: array
          items:
            properties:
              id:
                description: id
                type: number
                example: 67
                nullable: false
              name:
                description: 'Name of team'
                type: string
                example: 'Team alpha'
                nullable: false
              updated_at:
                description: 'Updated at'
                type: string
                example: '2024-07-16 14:52:33'
                nullable: false
              created_at:
                description: 'Created at'
                type: string
                example: '2024-07-16 14:52:33'
                nullable: false
            type: object
          nullable: true
        profiles:
          description: 'Profile in which user is assigned to'
          type: array
          items:
            properties:
              id:
                description: id
                type: string
                example: 483197bf-966b-43ae-ae52-541bcd0de607
                nullable: false
              review_status:
                type: string
                enum:
                  - Processed
                  - 'Needs Review'
                  - 'Additional Info Required'
                  - Approved
                  - Declined
                  - Archived
                example: Processed
                nullable: false
              updated_at:
                description: 'Updated at'
                type: string
                example: '2024-07-16 14:52:33'
                nullable: false
              created_at:
                description: 'Created at'
                type: string
                example: '2024-07-16 14:52:33'
                nullable: false
            type: object
          nullable: true
        updated_at:
          description: 'Updated at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
        created_at:
          description: 'Created at'
          type: string
          example: '2024-07-16 14:52:33'
          nullable: false
      type: object
  responses:
    '400':
      description: 'Bad Request'
      content:
        application/json:
          schema:
            properties:
              message:
                description: 'Description of error message'
                type: string
                example: 'Bad Request'
            type: object
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            properties:
              message:
                description: 'Description of error message'
                type: string
                example: Unauthenticated.
            type: object
    '404':
      description: 'Not Found'
      content:
        application/json:
          schema:
            properties:
              message:
                description: 'Description of error message'
                type: string
                example: 'Not Found'
            type: object
    '422':
      description: 'Unprocessable Content'
      content:
        application/json:
          schema:
            properties:
              message:
                description: 'Description of error message'
                type: string
                example: 'The example field code is required.'
              example_field:
                description: 'Name of request body field with a list of validation errors'
                type: array
                items:
                  type: string
                  example: 'The example field has already been taken.'
            type: object
    '429':
      description: 'Too Many Requests'
      content:
        application/json:
          schema:
            properties:
              message:
                description: 'Description of error message'
                type: string
                example: 'Too Many Attempts.'
            type: object
  parameters:
    Authorization:
      name: Authorization
      in: header
      description: 'Bearer token'
      schema:
        type: string
        nullable: false
    id:
      name: id
      in: path
      description: Id
      required: true
      schema:
        type: string
        nullable: false
    userId:
      name: userId
      in: path
      description: 'User Id'
      required: true
      schema:
        type: string
        nullable: false
    uuid:
      name: uuid
      in: path
      description: Uuid
      required: true
      schema:
        type: string
        nullable: false
    order_by:
      name: order_by
      in: query
      description: 'Order results in ascending (`asc`) or descending (`desc`) order. This parameter only works when `sort_by` is specified.'
      schema:
        type: string
        enum:
          - desc
          - asc
    sort_by:
      name: sort_by
      in: query
      description: "The `sort_by` parameter allows you to specify most fields to sort the results in either ascending (`asc`) or descending (`desc`) order.<br>\nThis parameter works in conjunction with `order_by`. If `order_by` is not specified, the default sorting order is ascending (`asc`).\n"
      schema:
        type: string
    page:
      name: page
      in: query
      description: 'Current page number'
      schema:
        type: string
  headers:
    X-RateLimit-Remaining:
      description: 'Number of request remaining'
      schema:
        type: number
        example: 9
        nullable: false
    X-RateLimit-Limit:
      description: 'Number of requests allowed'
      schema:
        type: number
        example: 50
        nullable: false
tags:
  -
    name: Checklists
    description: Checklists
  -
    name: Analysis
    description: Analysis
  -
    name: Documents
    description: Documents
  -
    name: Forms
    description: Forms
  -
    name: Representatives
    description: Representatives
  -
    name: Profiles
    description: Profiles
  -
    name: Search
    description: Search
  -
    name: Users
    description: Users
