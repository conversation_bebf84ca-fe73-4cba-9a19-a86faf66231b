identity-verification-service:
  enabled: false

  image:
    repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo
    branch: master
    name: identity-verification-service
    tagOverride: jenkins-pipeline-will-add-build-version

  pod:
    serviceAccountName: identity-verification-service-sa
    labels:
      buildnumber: "jenkins-pipeline-will-add-build-label"

  podmonitoring:
    enabled: true
    endpoints:
      - port: 8080
        path: /internal/metrics
        interval: 30s

  cloudSqlSideCar:
    enabled: true
    name: cloud-sql-proxy
    image:
      repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/gcr.io/cloudsql-docker/gce-proxy
      tag: 1.29.0
    databaseInstances:
      - name: identity-verification-service-cloudsql-instance
        port: 5432
        region: europe-west4
    memory: 512Mi
    cpu: 250m

  container:
    properties:
      GCP_LOGGING: "true"

    # XXX needs to be replaced with the corresponding workstream value, which can be found in this file:https://github.com/gumtree-gcp/terraform/blob/master/gum-secrets/secret.tf
    secrets:
      - name: DB_USER
        key: XXX-identity-verification-service-db-user
        version: latest
      - name: DB_PASSWORD
        key: XXX-identity-verification-service-db-password
        version: latest

    livenessProbe:
      httpGet:
        path: /internal/health/liveness
        port: 8080
    readinessProbe:
      httpGet:
        path: /internal/health/readiness
        port: 8080

  hpa:
    enabled: true
    minReplicas: 1
    maxReplicas: 1

  service:
    enabled: true
    ports:
      - protocol: TCP
        port: 80
        targetPort: 8080
        name: http

  virtualservice:
    enabled: true
    gateways:
      - istio-system/istio-internal-ingressgateway # Expose only through the Internal gateway
    hosts:
      - '{{ printf "%s.%s.%s.gumtree.cloud" .Release.Name .Values.region .Values.env }}'
    http:
      - route:
          - destination:
              host: identity-verification-service
              port:
                number: 80

identity-verification-service-db:
  enabled: false

  image:
    repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/identity-verification-service-db
    tag: jenkins-pipeline-will-add-build-label

  container:
    properties:
      DB_ACTION: "updatesql"
      DB_URL: "**************************************************************"
    secrets:
      - name: DB_USER
        key: XXX-identity-verification-service-db-user
        version: latest
      - name: DB_PASSWORD
        key: XXX-identity-verification-service-db-password
        version: latest

  pod:
    serviceAccountName: identity-verification-service-sa

  cloudSqlProxy:
    name: cloud-sql-proxy
    image:
      repository: gcr.io/cloudsql-docker/gce-proxy
      tag: 1.33.1-buster
    region: europe-west4
    databaseInstance: identity-verification-service-cloudsql-instance
    port: "5432"
    memory: 1Gi
    cpu: 500m