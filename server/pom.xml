<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.gumtree.tns</groupId>
    <artifactId>identity-verification-service</artifactId>
    <version>3.0-SNAPSHOT</version>
  </parent>

  <groupId>com.gumtree.tns.identity-verification-service</groupId>
  <artifactId>server</artifactId>
  <packaging>jar</packaging>
  <name>Identity Verification Service Server</name>
  <description>Identity Verification Service Server</description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.gumtree.shared-gcp-maven-bom</groupId>
        <artifactId>shared-gcp-maven-bom-chassis</artifactId>
        <version>2024-12-10-14-45-56.master.1035670</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>com.gumtree.shared-gcp-spring-boot</groupId>
      <artifactId>jetty-server</artifactId>
      <version>5.4.9672a23</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <!-- Starter for Stackdriver Trace -->
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>spring-cloud-gcp-starter-trace</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>spring-cloud-gcp-starter-logging</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.flogger</groupId>
      <artifactId>flogger</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.flogger</groupId>
      <artifactId>flogger-system-backend</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.flogger</groupId>
      <artifactId>flogger-slf4j-backend</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>io.swagger.core.v3</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>2.2.0</version>
    </dependency>

    <!-- Client Generation -->
    <dependency>
      <groupId>com.gumtree.shared-common.shared-api-base-contract</groupId>
      <artifactId>base-contract</artifactId>
      <version>2.1.ff0d55d</version>
      <type>yaml</type>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.openapitools</groupId>
      <artifactId>jackson-databind-nullable</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>

    <!-- Metrics -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>

    <!-- Testing -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
  </dependencies>
</project>

