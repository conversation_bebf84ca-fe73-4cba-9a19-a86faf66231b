package com.gumtree.tns.utils;

import org.springframework.http.HttpStatus;
import com.gumtree.tns.model.ApiError;


public final class ErrorUtils {

  private ErrorUtils() {
  }

  public static ApiError createUnknownApiError(HttpStatus status) {
    return new ApiError().code(status.toString())
        .type("unknown-000")
        .title("unknown error")
        .status(status.value())
        .detail("Unexpected error occurred while processing request, contact maintainers.");
  }

}
