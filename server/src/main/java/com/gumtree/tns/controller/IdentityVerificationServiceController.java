package com.gumtree.tns.controller;

import com.google.common.flogger.FluentLogger;
import com.google.common.flogger.StackSize;
import lombok.AllArgsConstructor;
import com.gumtree.tns.service.IdentityVerificationServiceService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import com.gumtree.exception.BaseException;
import com.gumtree.exception.ErrorCode;
import com.gumtree.tns.api.ExampleApi;
import com.gumtree.tns.model.SampleResponse;

@AllArgsConstructor
@RestController
public class IdentityVerificationServiceController implements ExampleApi {

  private static final FluentLogger log = FluentLogger.forEnclosingClass();

  private final IdentityVerificationServiceService identityVerificationServiceService;

  public ResponseEntity<SampleResponse> hello(String who) {

    if ("exception".equals(who)) {

      var e = new BaseException(ErrorCode.RESOURCE_NOT_FOUND_ERROR.getApiError("Feed"));

      log.atSevere().withStackTrace(StackSize.FULL).withCause(e).log("%s Severe MESSAGE LOGGED",
          e.getMessage());

      throw e;

    }

    return ResponseEntity.ok(new SampleResponse().message(identityVerificationServiceService.sayHello(who)));
  }

}
