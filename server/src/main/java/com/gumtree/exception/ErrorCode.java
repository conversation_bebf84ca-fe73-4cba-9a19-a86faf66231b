package com.gumtree.exception;

import com.google.cloud.spring.logging.TraceIdLoggingEnhancer;
import org.springframework.http.HttpStatus;
import com.gumtree.tns.model.ApiError;

/**
 * Base enum for creating ApiError Instance.
 * type – a URI identifier that categorises the error (default blank)
 * title – a brief, human-readable message about the error
 * status – the HTTP response code (optional)
 * detail – a human-readable explanation of the error
 * instance – a URI that identifies the specific occurrence of the error
 */
public enum ErrorCode {

  //400
  INVALID_REQUEST_HEADER(
      "https://example.net/invalid",
      "Invalid request header",
      HttpStatus.BAD_REQUEST,
      "Invalid request header : %s",
      "%s",
      "ERR-"+HttpStatus.BAD_REQUEST.value(),
      "%s"),
  INVALID_REQUEST_PARAMETER(
      "https://example.net/not-found-error",
      "InvalidParameter",
      HttpStatus.BAD_REQUEST,
      "Invalid Request Parameters",
      "%s",
      "ERR-"+HttpStatus.BAD_REQUEST.value(),
      "%s"),
  // 404
  RESOURCE_NOT_FOUND_ERROR(
      "https://example.net/not-found-error",
      "Resource not found",
      HttpStatus.NOT_FOUND,
      "Error occoured while featching %s",
      "%s",
      "ERR-"+HttpStatus.NOT_FOUND.value(),
      "%s"),
  IMAGE_TOO_LARGE_ERROR(
      "https://example.net/payload-too-large-error",
      "Image too large",
      HttpStatus.PAYLOAD_TOO_LARGE,
      "Error occoured while uploading image, max size %d",
      "%s",
      "ERR-"+HttpStatus.PAYLOAD_TOO_LARGE.value(),
      "%s"),
  // 500
  INTERNAL_SERVER_ERROR(
      "https://example.net/internal-error",
      "Internal server error",
      HttpStatus.INTERNAL_SERVER_ERROR,
      "Internal Server Error: %s",
      "%s",
      "ERR-"+HttpStatus.INTERNAL_SERVER_ERROR.value(),
      "%s"),
  UNCHECKED_IO_ERROR(
      null,
      "EPS IO error",
      HttpStatus.INTERNAL_SERVER_ERROR,
      "IO Error: %s",
      "%s",
      "EPS-002",
      "%s"),
  BAD_GATEWAY(
      null,
      "Bad Gateway error",
      HttpStatus.BAD_GATEWAY,
      "Error occurred in downstream service: %s",
      "%s",
      "ERR-"+HttpStatus.BAD_GATEWAY.value(),
      "%s");

  private final String type;

  private final String title;

  private final HttpStatus status;

  private final String detail;

  private final String instance;

  private final String code;

  private final String traceId;


  public ApiError getApiError() {
    var error = new ApiError();
    error.setType(this.type);
    error.setTitle(this.title);
    error.setStatus(status.value());
    error.setDetail(this.detail);
    error.setInstance(this.instance);
    error.code(this.code);
    error.setTraceId(TraceIdLoggingEnhancer.getCurrentTraceId());
    return error;
  }

  public ApiError getApiError(Object... v) {
    var apiError = getApiError();
    apiError.setDetail(detail.formatted(v));
    return apiError;
  }

  public String getDescription(Object... v) {
    return detail.formatted(v);
  }

  public HttpStatus getStatus() {
    return this.status;
  }

  public static ErrorCode fromCode(String text) {
    for (ErrorCode b : ErrorCode.values()) {
      if (b.code.equalsIgnoreCase(text)) {
        return b;
      }
    }
    return null;
  }

  ErrorCode(String type, String title, HttpStatus status, String detail, String instance,
      String code, String traceId) {
    this.type = type;
    this.title = title;
    this.status = status;
    this.detail = detail;
    this.instance = instance;
    this.code = code;
    this.traceId = traceId;
  }
}
