package com.gumtree.exception.handler;

import lombok.extern.flogger.Flogger;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import com.gumtree.exception.ErrorCode;
import com.gumtree.tns.model.ApiError;

/**
 * Global exception handler will catch and translate all the exception which are not handled by
 * ServiceExceptionHandler {@link com.gumtree.exception.handler.ServiceExceptionHandler}
 */
@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
@Flogger
public class GenericExceptionHandler {

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ApiError> handleGenericException(Exception ex, WebRequest request) {
    log.atSevere().withCause(ex).log("%s Exception thrown", ex.getMessage());
    var errorCode = ErrorCode.INTERNAL_SERVER_ERROR;
    var apiError = errorCode.getApiError();
    apiError.detail(ex.getMessage());
    return new ResponseEntity<>(apiError, errorCode.getStatus());
  }

}
