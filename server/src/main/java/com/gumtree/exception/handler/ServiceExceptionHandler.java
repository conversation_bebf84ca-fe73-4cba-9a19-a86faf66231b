package com.gumtree.exception.handler;

import com.gumtree.tns.model.ApiError;
import com.gumtree.tns.model.ApiErrorDetail;
import com.gumtree.tns.model.ApiErrorDetail.LocationEnum;
import com.gumtree.exception.BaseException;
import com.gumtree.exception.ErrorCode;
import lombok.extern.flogger.Flogger;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;
import java.io.UncheckedIOException;

/**
 * This class will handle all the runtime exceptions extending base exception. Other exceptions have
 * to declared handled explicitly in this file.
 */
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
@Flogger
public class ServiceExceptionHandler {

  /**
   * Every custom exception extending BaseException will be handled by this method.
   *
   * @param e
   * @param request
   * @return
   */
  @ExceptionHandler(BaseException.class)
  public ResponseEntity<Object> handleBaseException(BaseException e, HttpServletRequest request) {
    return handleApiError(e.getApiError(), request);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException e,
      HttpServletRequest httpServletRequest) {
    var apiError = ErrorCode.INVALID_REQUEST_PARAMETER.getApiError();
    apiError.setDetails(e.getBindingResult().getFieldErrors().stream().map(fieldError -> {
      var apiErrorDetail = new ApiErrorDetail();
      apiErrorDetail.setField(fieldError.getField());
      apiErrorDetail.setLocation(LocationEnum.BODY);
      apiErrorDetail.setValue(
          fieldError.getRejectedValue() == null ? null : fieldError.getRejectedValue().toString());
      apiErrorDetail.setReason(fieldError.getDefaultMessage());
      return apiErrorDetail;
    }).toList());
    log.atInfo().withCause(e).log("MethodArgumentNotValid Error %s", apiError);
    return handleApiError(apiError, httpServletRequest);
  }

  @ExceptionHandler(MissingRequestHeaderException.class)
  public ResponseEntity<Object> handleMissingRequestHeaderExceptionException(
      MissingRequestHeaderException e, HttpServletRequest request) {
    var detail = "MissingRequestHeaderException thrown";
    log.atWarning().withCause(e).log(detail);
    return handleApiError(ErrorCode.INVALID_REQUEST_HEADER.getApiError(detail), request);
  }

  @ExceptionHandler(UncheckedIOException.class)
  public ResponseEntity<Object> handleUncheckedIOException(UncheckedIOException e, HttpServletRequest request) {
    ApiError apiError = ErrorCode.UNCHECKED_IO_ERROR.getApiError(e.getMessage());
    log.atSevere().withCause(e).log("IO Error: %s", apiError);
    return handleApiError(apiError, request);
  }

  @ExceptionHandler(MaxUploadSizeExceededException.class)
  public ResponseEntity<Object> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e,
      HttpServletRequest request) {
    ApiError apiError = ErrorCode.IMAGE_TOO_LARGE_ERROR.getApiError(e.getMaxUploadSize());
    log.atInfo().withCause(e).log("Max Image Size Error: %s", apiError);
    return handleApiError(apiError, request);
  }

  private ResponseEntity<Object> handleApiError(ApiError apiError, HttpServletRequest request) {
    apiError.setInstance(request.getRequestURI());
    return new ResponseEntity<>(apiError, HttpStatus.valueOf(apiError.getStatus()));
  }

}
