package com.gumtree.tns.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import com.gumtree.tns.service.IdentityVerificationServiceService;

@WebMvcTest(IdentityVerificationServiceController.class)
class IdentityVerificationServiceControllerMvcTest {

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private IdentityVerificationServiceService identityVerificationServiceService;

  @Test
  void hello() {

  }

}
