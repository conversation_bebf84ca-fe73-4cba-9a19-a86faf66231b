package com.gumtree.exception.handler;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.flogger.Flogger;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import com.gumtree.exception.BaseException;
import com.gumtree.tns.model.ApiError;

@Flogger
@ExtendWith(MockitoExtension.class)
class ServiceExceptionHandlerTest {

  @Mock
  private HttpServletRequest httpRequest;

  @InjectMocks
  private ServiceExceptionHandler serviceExceptionHandler;

  @Test
  void handleBaseException() {

    ApiError expected = new ApiError().status(HttpStatus.INTERNAL_SERVER_ERROR.value())
        .title("internal server error");
    BaseException baseException = new BaseException(expected);

    ResponseEntity<Object> responseEntity = serviceExceptionHandler.handleBaseException(baseException, httpRequest);
    assertThat(responseEntity.getStatusCode(), is(HttpStatus.INTERNAL_SERVER_ERROR));
    ApiError actual = (ApiError) responseEntity.getBody();
    assertThat(actual, is(expected));
  }

  @Test
  void handleMethodArgumentNotValid() {

    MethodParameter methodParameter = mock(MethodParameter.class);
    BindingResult bindingResult = mock(BindingResult.class);

    String objectName = "vehicle";
    String field = "model";
    String defaultMessage = "model missing";

    FieldError fieldError = new FieldError(objectName, field, defaultMessage);
    List<FieldError> fieldErrors = Arrays.asList(fieldError);
    when(bindingResult.getFieldErrors()).thenReturn(fieldErrors);
    MethodArgumentNotValidException methodArgumentNotValidException
        = new MethodArgumentNotValidException(methodParameter, bindingResult);

    ResponseEntity<Object> response =
        serviceExceptionHandler.handleMethodArgumentNotValid(methodArgumentNotValidException, httpRequest);

    assertThat(response.getStatusCode(), is(HttpStatus.BAD_REQUEST));

    ApiError apiError = (ApiError) response.getBody();
    assertThat(apiError.getDetails().size(), is(1));
  }

  @Test
  void handleMissingRequestHeaderExceptionException() throws ClassNotFoundException {

    String headerName = "x-some-header";
    MethodParameter methodParameter = mock(MethodParameter.class);
    Class<?> clazz = Class.forName("java.lang.String");

    doReturn(clazz).when(methodParameter).getNestedParameterType();

    MissingRequestHeaderException missingRequestHeaderException = new MissingRequestHeaderException(headerName, methodParameter);
    ResponseEntity<Object> responseEntity = serviceExceptionHandler.handleMissingRequestHeaderExceptionException(
        missingRequestHeaderException, httpRequest);

    assertThat(responseEntity.getStatusCode(), is(HttpStatus.BAD_REQUEST));
    log.atInfo().log("ApiError: %s", responseEntity.getBody());
  }

  @Test
  void handleUncheckedIOException() {

    UncheckedIOException uncheckedIOException = new UncheckedIOException(new IOException("some problem"));
    ResponseEntity<Object> responseEntity = serviceExceptionHandler.handleUncheckedIOException(
        uncheckedIOException, httpRequest);

    assertThat(responseEntity.getStatusCode(), is(HttpStatus.INTERNAL_SERVER_ERROR));
    ApiError apiError = (ApiError) responseEntity.getBody();
    assertThat(apiError.getStatus(), is(HttpStatus.INTERNAL_SERVER_ERROR.value()));
    assertThat(apiError.getDetail(), is("IO Error: java.io.IOException: some problem"));
    assertThat(apiError.getTitle(), is("EPS IO error"));
    log.atInfo().log("ApiError: %s", apiError);
  }

  @Test
  void handleMaxUploadSizeExceededException() {

    MaxUploadSizeExceededException maxUploadSizeExceededException = new MaxUploadSizeExceededException(50000);
    ResponseEntity<Object> responseEntity = serviceExceptionHandler.handleMaxUploadSizeExceededException(
        maxUploadSizeExceededException, httpRequest);

    assertThat(responseEntity.getStatusCode(), is(HttpStatus.PAYLOAD_TOO_LARGE));
    ApiError apiError = (ApiError) responseEntity.getBody();
    assertThat(apiError.getStatus(), is(HttpStatus.PAYLOAD_TOO_LARGE.value()));
    assertThat(apiError.getDetail(), is("Error occoured while uploading image, max size 50000"));
    assertThat(apiError.getTitle(), is("Image too large"));
    log.atInfo().log("ApiError: %s", apiError);
  }

}
