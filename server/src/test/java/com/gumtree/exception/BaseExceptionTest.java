package com.gumtree.exception;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

import java.io.IOException;
import org.junit.jupiter.api.Test;
import com.gumtree.tns.model.ApiError;

class BaseExceptionTest {

  @Test
  void shouldConstructWithApiError() {
    ApiError expected = new ApiError().detail("some detail");
    BaseException baseException = new BaseException(expected);
    assertThat(baseException.getApiError(), is(expected));
    assertThat(baseException.getMessage(), is("some detail"));
  }

  @Test
  void shouldConstructWithApiErrorAndThrowable() {
    ApiError expected = new ApiError().detail("some detail");
    IOException ioException = new IOException("test");
    BaseException baseException = new BaseException(expected, ioException);
    assertThat(baseException.getApiError(), is(expected));
    assertThat(baseException.getMessage(), is("some detail"));
    assertThat(baseException.getCause(), is(ioException));
  }

}
