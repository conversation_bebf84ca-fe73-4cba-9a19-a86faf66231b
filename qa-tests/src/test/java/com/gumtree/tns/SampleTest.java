package com.gumtree.tns;

import com.gumtree.tns.identity_verification_service.service.model.SampleResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class SampleTest extends BaseTest {

    @Test
    @DisplayName("Should say hello..")
    public void shouldSayHello() {
        // given
        var user = testDataApi.createUniqueUser();
        var name = "tony";

        // when
        var response = exampleApi.hello(name);

        // then
        assertThat(response).isEqualTo(new SampleResponse().message("Hello " + name));
    }
}
