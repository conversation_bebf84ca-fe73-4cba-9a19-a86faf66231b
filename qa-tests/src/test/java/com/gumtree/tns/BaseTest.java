package com.gumtree.tns;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.common.properties.GtProps;
import com.gumtree.tns.identity_verification_service.service.ExampleApi;
import com.gumtree.tds.TdsUserApiFacade;
import com.gumtree.testdata.service.UsersApi;
import feign.Feign;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import org.apache.commons.configuration.ConfigurationException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.gumtree.tns.QaConfigProps.SERVICE;
import static com.gumtree.tns.QaConfigProps.TDS;

public abstract class BaseTest {
    private static final Logger logger = LoggerFactory.getLogger("Test");


    protected TdsUserApiFacade testDataApi;

    protected ExampleApi exampleApi;

    @BeforeAll
    public static void beforeAll() throws ConfigurationException {
        GtPropertiesInitializer.init("identity-verification-service-qa-tests");
    }

    @BeforeEach
    public void before(TestInfo testInfo) {
        this.exampleApi = buildExampleApi();
        this.testDataApi = new TdsUserApiFacade(tdsUsersApi());

        logger.info("=================== {}: {} ===================", testInfo.getTestClass().map(Class::getName).orElse("-"),
                testInfo.getDisplayName());
    }

    /* -- Setup Clients --*/

    private static ExampleApi buildExampleApi() {
        return Feign.builder()
                .encoder(new JacksonEncoder(exampleApiObjectMapper()))
                .decoder(new JacksonDecoder(exampleApiObjectMapper()))
                .logger(new Slf4jLogger())
                .logLevel(feign.Logger.Level.FULL)
                .target(ExampleApi.class, GtProps.getStr(SERVICE));
    }

    private static UsersApi tdsUsersApi() {
        ObjectMapper bapiObjectMapper = tdsObjectMapper();

        return Feign.builder()
                .encoder(new JacksonEncoder(bapiObjectMapper))
                .decoder(new JacksonDecoder(bapiObjectMapper))
                .logger(new Slf4jLogger())
                .logLevel(feign.Logger.Level.FULL)
                .target(UsersApi.class, GtProps.getStr(TDS));
    }

    private static ObjectMapper exampleApiObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        return objectMapper;
    }

    private static ObjectMapper tdsObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        return objectMapper;
    }

}
